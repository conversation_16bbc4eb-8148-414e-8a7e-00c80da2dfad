<?php

declare(strict_types=1);

namespace App;

use App\Enums\WishListPrivacyLevel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use InvalidArgumentException;

/**
 * WishList Model
 *
 * Represents a customer's wishlist with privacy controls, address linking,
 * and item management functionality.
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int $customer_id
 * @property int|null $address_id
 * @property Carbon|null $desired_date
 * @property WishListPrivacyLevel $privacy_level
 * @property string|null $share_token
 * @property bool $allow_purchase_from_other_products
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read Customer $customer
 * @property-read Address|null $address
 * @property-read Collection<int, WishListItem> $items
 * @property-read int $items_count
 *
 * Scopes:
 * @method static Builder|WishList public()
 * @method static Builder|WishList shared()
 * @method static Builder|WishList private()
 * @method static Builder|WishList forCustomer(Customer $customer)
 */
class WishList extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'customer_id',
        'address_id',
        'desired_date',
        'privacy_level',
        'share_token',
        'allow_purchase_from_other_products'
    ];

    protected $casts = [
        'desired_date' => 'date',
        'privacy_level' => WishListPrivacyLevel::class,
        'customer_id' => 'integer',
        'address_id' => 'integer',
        'allow_purchase_from_other_products' => 'boolean',
    ];

    protected $appends = [
        'items_count',
        'purchased_count',
        'url',
        'customer_name',
    ];

    /**
     * Model boot method - handles automatic share token generation
     *
     * Automatically generates share tokens when the wishlist is marked as shared
     * and removes tokens when the wishlist becomes private.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::saved(function (WishList $wishlist) {
            // Generate share token if wishlist is shared
            if ($wishlist->privacy_level === WishListPrivacyLevel::SHARED && !$wishlist->share_token) {
                $wishlist->share_token = Str::random(32);
                $wishlist->saveQuietly();
            }
        });
    }

    /**
     * Get the customer that owns this wishlist
     *
     * @return BelongsTo<Customer, WishList>
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get all items in this wishlist
     *
     * @return HasMany<WishListItem>
     */
    public function items(): HasMany
    {
        return $this->hasMany(WishListItem::class)->orderBy('order');
    }

    /**
     * Get the delivery address for this wishlist
     *
     * @return BelongsTo<Address, WishList>
     */
    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class);
    }

    /**
     * Get the count of items in this wishlist (accessor)
     *
     * @return int Number of items in the wishlist
     */
    public function getItemsCountAttribute(): int
    {
        return (int) $this->items()->sum('quantity');
    }

    /**
     * Get the count of purchased items in this wishlist (accessor)
     *
     * @return int Number of purchased items in the wishlist
     */
    public function getPurchasedCountAttribute(): int
    {
        return (int) $this->items()->sum('purchase_quantity');
    }

    /**
     * Get the URL for this wishlist
     *
     * Generates a URL based on the wishlist's privacy level:
     * - Public: direct URL to the wishlist
     * - Shared: URL with share token
     * - Private: direct URL to the wishlist (accessible only by owner)
     *
     * @return string The URL for this wishlist
     * @throws InvalidArgumentException if share token is required but not set
     */
    public function getUrlAttribute(): string
    {
        if ($this->privacy_level === WishListPrivacyLevel::SHARED){
            if (!$this->share_token) {
                throw new InvalidArgumentException("Share token is required for shared wishlists.");
            }
            return url("/wishlists/{$this->id}?token={$this->share_token}");
        }

        return url("/wishlists/{$this->id}");
    }

    /**
     * Get the name of the customer who owns this wishlist
     *
     * @return string The customer's name
     */
    public function getCustomerNameAttribute(): string
    {
        return $this->customer->name ?? 'Unknown Customer';
    }
    /**
     * Scope query to only public wishlists
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopePublic(Builder $query): Builder
    {
        return $query->where('privacy_level', WishListPrivacyLevel::PUBLIC->value);
    }

    /**
     * Scope query to only shared wishlists
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeShared(Builder $query): Builder
    {
        return $query->where('privacy_level', WishListPrivacyLevel::SHARED->value);
    }

    /**
     * Scope query to only private wishlists
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopePrivate(Builder $query): Builder
    {
        return $query->where('privacy_level', WishListPrivacyLevel::PRIVATE->value);
    }

    /**
     * Scope query to wishlists owned by a specific customer
     *
     * @param Builder $query
     * @param Customer $customer The customer to filter by
     * @return Builder
     */
    public function scopeForCustomer(Builder $query, Customer $customer): Builder
    {
        return $query->where('customer_id', $customer->id);
    }
}
