<?php

declare(strict_types=1);

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Bag Model
 *
 * Represents a shopping bag or cart in the application.
 * It can contain various items, including products and gift cards.
 *
 * @property int $customer_id
 * @property int $recipient_id
 * @property int $wishlist_id
 * @property string $type
 * @property string $model_type
 * @property int $model_id
 * @property string $item_key
 * @property int $quantity
 * @property bool $later
 * @property bool $active
 * @property array $meta
 *
 * @property-read Model $model
 * @property-read Customer $customer
 * @property-read Customer $recipient
 * @property-read WishList $wishlist
 */
class Bag extends Model
{
    public $fillable = [
        'customer_id',
        'recipient_id',
        'wishlist_id',
        'type',
        'model_type',
        'model_id',
        'item_key',
        'quantity',
        'later',
        'active',
        'meta'
    ];

    public $casts = [
        'later' => 'boolean',
        'active' => 'boolean',
        'meta' => 'array',
    ];

    public $table = 'bag';

    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    public function getPrice(): float|int
    {
        return $this->model->price * $this->quantity;
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function recipient(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'recipient_id');
    }

    public function wishlist(): BelongsTo
    {
        return $this->belongsTo(WishList::class);
    }

    public function getFrontEndAttribute($quantity = 1)
    {
        if ($model = $this->model) {
            return array_merge(
                $model->getFrontEndAttribute($quantity),
                $this->meta ?? [],
                [
                    'item_key' => $this->item_key
                ]
            );
        } elseif ($this->model_type == GiftCard::class) {
            return array_merge(
                (new GiftCard)->getFrontEndAttribute($this->meta),
                ['item_key' => $this->item_key]
            );
        }
    }

    public function getExtendedDuration()
    {
        return optional($this->model)->getExtendedDuration();
    }
}
