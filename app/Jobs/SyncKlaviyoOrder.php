<?php

namespace App\Jobs;

use App\Order;
use App\Services\KlaviyoService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncKlaviyoOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Order $order)
    {
        $this->queue = 'sync';
    }

    public function handle(): void
    {
        // Early return if <PERSON><PERSON><PERSON><PERSON> is disabled
        if (!config('klaviyo.enabled')) {
            return;
        }

        try {
            $service = new KlaviyoService();
            $service->syncOrder($this->order);
        } catch (\Throwable $e) {
            Log::error('SyncKlaviyoOrder job failed', [
                'order_id' => $this->order->id ?? null,
                'error' => $e->getMessage(),
            ]);
            // swallow exception to prevent noisy retries if failure is permanent
        }
    }
}

