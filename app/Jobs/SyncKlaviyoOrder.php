<?php

namespace App\Jobs;

use App\Order;
use App\Services\KlaviyoService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncKlaviyoOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Order $order)
    {
    }

    public function handle(): void
    {
        try {
            // The service method handles the config('klaviyo.enabled') guard and logging
            $service = new KlaviyoService();
            $service->syncOrder($this->order);
        } catch (\Throwable $e) {
            Log::error('SyncKlaviyoOrder job failed', [
                'order_id' => $this->order->id ?? null,
                'error' => $e->getMessage(),
            ]);
            // Do not rethrow to avoid unnecessary retries for permanent failures
        }
    }
}

