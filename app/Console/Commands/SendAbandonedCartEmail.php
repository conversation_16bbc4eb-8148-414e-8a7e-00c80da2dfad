<?php

namespace App\Console\Commands;

use App\Customer;
use App\Http\Controllers\Api\EmailsController;
use Illuminate\Console\Command;

class SendAbandonedCartEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:sendAbandonedCart';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sends an email every four hours to all abandoned carts';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        l('Abandoned Cart Email');
        $customerIds = self::pluck('customer_id')->unique();
        $customerIds->each(function ($id) {
            dispatch(function () use ($id) {
                $customer = Customer::find($id);
                if ($customer->abandoned()) {
                    EmailsController::SendAbandonedCart($customer);
                    $customer->setBagsAsReminded();
                }
            });
        });
    }
}
