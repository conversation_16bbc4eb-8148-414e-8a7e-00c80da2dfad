<?php

declare(strict_types=1);

namespace App\Enums;

enum WishListPrivacyLevel: string
{
    case PUBLIC = 'public';
    case SHARED = 'shared';
    case PRIVATE = 'private';

    public function getLabel(): string
    {
        return match ($this) {
            self::PUBLIC => 'Public',
            self::SHARED => 'Shared',
            self::PRIVATE => 'Private',
        };
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::PUBLIC => 'Visible to everyone',
            self::SHARED => 'Accessible via shared link',
            self::PRIVATE => 'Only visible to you',
        };
    }

    public static function toArray(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    public static function toNovaOptions(): array
    {
        return array_reduce(self::cases(), function ($options, $case) {
            $options[$case->value] = $case->getLabel();
            return $options;
        }, []);
    }
}
