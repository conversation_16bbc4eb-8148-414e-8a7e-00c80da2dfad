<?php

declare(strict_types=1);

namespace App\Enums;

enum TaxExemptType: string
{
    case GOVERNMENT = 'government';
    case OTHER = 'other';
    case WHOLESALE = 'wholesale';

    public function getLabel(): string
    {
        return match ($this) {
            self::GOVERNMENT => 'Government',
            self::OTHER => 'Other',
            self::WHOLESALE => 'Wholesale',
        };
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::GOVERNMENT => 'Government organization tax exemption',
            self::OTHER => 'Other type of tax exemption',
            self::WHOLESALE => 'Wholesale business tax exemption',
        };
    }

    /**
     * Get all tax exempt types as array for forms/selects
     *
     * @return array<string, string> Array with value => label pairs
     */
    public static function toArray(): array
    {
        return array_combine(
            array_column(self::cases(), 'value'),
            array_map(fn(self $case) => $case->getLabel(), self::cases())
        );
    }

    /**
     * Get all tax exempt types as options for Nova select field
     *
     * @return array<string, string>
     */
    public static function toNovaOptions(): array
    {
        return self::toArray();
    }

    /**
     * Get tax exempt type from string value
     *
     * @param string|null $value
     * @return self|null
     */
    public static function fromValue(?string $value): ?self
    {
        if ($value === null) {
            return null;
        }

        return self::tryFrom($value);
    }
}
