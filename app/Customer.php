<?php

namespace App;

use App\Emails\AddSubscriber;
use App\Enums\TaxExemptType;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Traits\CustomerRecurringTrait;
use Illuminate\Notifications\Notifiable;
use App\Http\Controllers\TransactionController;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use App\Traits\CustomerSubscriptionTrait;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Carbon\Carbon;

/**
 * Customer Model
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property string $phone
 * @property Carbon|null $email_verified_at
 * @property string $password
 * @property array|null $recent
 * @property array|null $favorites
 * @property TaxExemptType $tax_exempt
 * @property int|null $group_id
 * @property string|null $remember_token
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read string $first_name
 * @property-read Address|null $address
 * @property-read Payment|null $payment
 */
class Customer extends Authenticatable implements HasMedia
{
    use InteractsWithMedia, CustomerSubscriptionTrait;
    use Notifiable;
    use CustomerRecurringTrait;

    protected $guarded = [];

    protected $casts = [
        'favorites' => 'array',
        'recent' => 'array',
        'tax_exempt' => TaxExemptType::class
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $appends = [
        'first_name',
        'address',
        'payment',
        'front_end_following',
        'subscription_status',
    ];

    /**
     * Get the UTM record associated with the customer
     */
    public function utm(): MorphOne
    {
        return $this->morphOne('App\Utm', 'model');
    }

    /**
     * Get all bag items for the customer
     */
    public function bag(): HasMany
    {
        return $this->hasMany(Bag::class);
    }

    /**
     * Get all follows for the customer
     */
    public function following(): HasMany
    {
        return $this->hasMany(Follow::class);
    }

    /**
     * Get all addresses for the customer
     */
    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'model');
    }

    /**
     * Get all payment methods for the customer
     */
    public function payments(): MorphMany
    {
        return $this->morphMany(Payment::class, 'model');
    }

    /**
     * Get the customer's membership
     */
    public function membership(): HasOne
    {
        return $this->hasOne('App\Membership');
    }

    /**
     * Get active subscriptions for the customer
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class)
            ->where(function ($query) {
                $query->where('subscriptions.status', '!=', 'canceled')
                    ->orWhere('subscriptions.status', null);
            });
    }


    /**
     * Get the customer's group
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo('App\Group');
    }

    /**
     * Get all orders for the customer
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get all returns through orders
     */
    public function returns(): HasManyThrough
    {
        return $this->hasManyThrough(Returns::class, Order::class);
    }

    /**
     * Get all wish lists for the customer
     */
    public function wishLists(): HasMany
    {
        return $this->hasMany(WishList::class);
    }

    /**
     * Get the customer's first name
     */
    public function getFirstNameAttribute(): string
    {
        return Str::before($this->name, ' ');
    }

    /**
     * Get the customer's default address
     */
    public function GetAddressAttribute(): ?Address
    {
        return $this->addresses->firstWhere('default', true) ?? $this->addresses->first();
    }

    /**
     * Get the customer's default payment method
     */
    public function GetPaymentAttribute(): ?Payment
    {
        return $this->payments->firstWhere('default', true) ?? $this->payments->first();
    }

    /**
     * Get available discounts for customers
     */
    public static function discounts(): SupportCollection
    {
        $discounts = Discount::user()
            ->active()
            // ->limit()
            ->automated()
            ->get();
        return $discounts->filter(function (Discount $discount): bool {
            return $discount->checkCustomerLimit();
        });
    }

    /**
     * Get or create a customer from array data
     *
     * @param array<string, mixed> $customer_array Customer data array
     */
    public static function getCustomer(array $customer_array): Customer
    {
        if (auth()->check()) {
            return auth()->user();
        }

        /** @var Customer $user */
        $user = self::firstOrCreate(
            ['email' => $customer_array['email']],
            [
                'name' => $customer_array['name'],
                'phone' => data_get($customer_array, 'shippingInfo.phone'),
            ]
        );

        if (isset($customer_array['loginInfo']) && !$user->password) {
            $password = decode(data_get($customer_array, 'loginInfo.password'));
            $user->update(['password' => Hash::make($password)]);

            auth()->login($user);

            try {
                $shipping = Arr::only(
                    $customer_array['shippingInfo'],
                    DB::getSchemaBuilder()->getColumnListing('addresses')
                );
                $shipping['default'] = true;
                $user->addresses()->create($shipping);
            } catch (Exception $e) {
            }

            try {
                $payment = Arr::only(
                    $customer_array['creditInfo'],
                    DB::getSchemaBuilder()->getColumnListing('payments')
                );
                $payment['default'] = true;
                $user->payments()->create($payment);
            } catch (Exception $e) {
            }
        }

        return $user->load(['payments', 'orders', 'addresses']);
    }

    /**
     * Get next subscription products
     *
     * @return SupportCollection<mixed>
     */
    public function nextSubscriptions(): SupportCollection
    {
        return $this->subscriptions->map->getFristProducts();
    }

    /**
     * Get customer's own discounts above minimum amount
     *
     * @param float $min Minimum amount
     */
    public function getOwnDiscounts(float $min): Collection
    {
        return $this->eligible()->active()->automated()->above($min)->get();
    }

    /**
     * Get following data formatted for frontend
     *
     * @return SupportCollection<string, SupportCollection<int>>
     */
    public function getFrontEndFollowingAttribute(): SupportCollection
    {
        return $this->following->map(function (Follow $follow): array {
            $class = explode('\\', $follow->model_type);
            return [
                'id' => $follow->model_id,
                'type' => Str::plural(strtolower($class[count($class) - 1])),
            ];
        })
            ->groupBy('type')
            ->map(function (SupportCollection $item): SupportCollection {
                return $item->map(function (array $i): int {
                    return $i['id'];
                });
            });
    }

    /**
     * Get bag items formatted for the frontend
     *
     * @param bool $later Whether to get "save for later" items
     * @return mixed
     */
    public function frontEndBag(bool $later = false)
    {
        return makeForFronEnd($this->bag->where('later', $later));
    }

    /**
     * Get abandoned bag items (not reminded)
     *
     * @return mixed
     */
    public function abandonedBag()
    {
        return makeForFronEnd($this->bag->filter(function (Bag $bag): bool {
            return !$bag->later && data_get($bag, 'meta.reminded') != true;
        }));
    }

    /**
     * Get IDs of abandoned bag items
     *
     * @return SupportCollection<int>
     */
    public function abandonedBagIds(): SupportCollection
    {
        return $this->bag->filter(function (Bag $bag): bool {
            return !$bag->later && data_get($bag, 'meta.reminded') != true;
        })->values()->pluck('id');
    }

    /**
     * Check if customer has abandoned cart
     */
    public function abandoned(): bool
    {
        return $this->bag->sortBy('updated_at')->last()->updated_at->diffInHours() >= settings()->getValue(
                'hours_to_send_abandoned_cart_email'
            );
    }

    /**
     * Mark all bag items as reminded
     */
    public function setBagsAsReminded(): void
    {
        $this->bag->each(function (Bag $b): void {
            $b->update(['meta->reminded' => true]);
        });
    }

    /**
     * Calculate total price of all bag items
     */
    public function getBagTotal(): float
    {
        $total = 0;
        foreach ($this->bag as $item) {
            $total += $item->getPrice();
        }
        return $total;
    }

    /**
     * Get applicable discounts for customer
     *
     * @return Collection|null
     */
    public function getApplicableDiscounts(): ?Collection
    {
        $min = $this->getBagTotal();
        $groupDiscounts = $this->group->getDiscounts($min);
        $ownDiscounts = $this->getOwnDiscounts($min);
        if ($groupDiscounts && $ownDiscounts) {
            return $groupDiscounts->merge($ownDiscounts);
        } else {
            return $groupDiscounts ? $groupDiscounts : $ownDiscounts;
        }
    }

    /**
     * Get digital products for customer
     */
    public function digitals(): HasMany
    {
        return $this->hasMany(Digital::class);
    }

    /**
     * Create a payment for the customer
     *
     * @param array<string, mixed> $data Payment data
     */
    public function createPayment(array $data): Payment
    {
        //todo
        //parse date ex: 12/10
        $payment = $this->payments()->create($data);
        // return '//todo';
        return $payment;
    }

    /**
     * Calculate customer's points and membership start date
     *
     * @return array{start: Carbon, points: int}
     */
    public function calculatePoints(): array
    {
        $start = $this->membership->getStart();
        return [
            'start' => $start,
            'points' => (int)$this->orders->where('created_at', '>', $start)->where('status', '!=', 'canceled')
                ->map(function (Order $purchase): float {
                    return $purchase['grand_total'] - $purchase['tax_amount'] - $purchase['shipping_amount'];
                })->sum()
        ];
    }

    /**
     * Calculate customer's membership status
     *
     * @return array{start: Carbon, points: int, status: string}
     */
    public function calculateStatus(): array
    { //this function will is not to be called when the points actually change.
        $pointsInfo = $this->calculatePoints();
        $status = RewardGroup::where('amount', '<', $pointsInfo['points'])->orderBy('amount', 'desc')->first(
        )['name'];
        return array_merge($pointsInfo, ['status' => $status]);
    }

    /**
     * Get new status for customer
     *
     * @return mixed
     */
    public function getNewStatus()
    {
    }

    /**
     * Get customer's phone number from first address
     */
    public function getPhoneNumberAttribute(): ?string
    {
        return optional($this->addresses->first())->phone;
    }

    /**
     * Check if customer has active membership
     */
    public function hasActiveMembership(): bool
    {
        return $this->membership && $this->membership->active;
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('file_upload')
            ->singleFile();
    }

    /**
     * Boot the model and set up event listeners
     */
    protected static function boot(): void
    {
        parent::boot();

        static::saved(function (Customer $customer): void {
            if (!$customer->getOriginal('tax_exempt') && $customer->tax_exempt) {
                TransactionController::AddCustomer($customer);
            }
            if ($customer->getOriginal('tax_exempt') && $customer->tax_exempt) {
                TransactionController::UpdateCustomer($customer);
            }
            if ($customer->getOriginal('tax_exempt') && !$customer->tax_exempt) {
                TransactionController::DeleteCustomer($customer);
            }
        });
    }

    /**
     * Check if email is subscribed
     *
     * @param string $email Email address to check
     */
    public function getIsSubscribedAttribute(string $email): string
    {
        return json_encode(
            data_get(
                forceArray(json_encode((new AddSubscriber)->get($email))),
                'response.State'
            ) == 'Active'
        );
    }
}
