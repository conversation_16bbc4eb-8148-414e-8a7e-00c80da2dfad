<?php

namespace App\Observers;

use App\AssociatedPurchase;
use App\Digital;
use App\GiftCard;
use App\GiftRecord;
use App\Http\Controllers\Api\EmailsController;
use App\Http\Controllers\CampaignMonitorController;
use App\Http\Controllers\GiftNotesController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\PosController;
use App\Http\Controllers\PosOnlineController;
use App\Http\Controllers\RouteForMeController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\UtmController;
use App\Membership;
use App\Order;
use App\Jobs\OrderCreate;
use App\Http\Controllers\ChargeController;
use App\Services\KlaviyoService;
use App\DTO\Klaviyo\CreateKlaviyoEventDTO;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Support\Facades\Log;

class OrderObserver implements ShouldHandleEventsAfterCommit
{
    public function created(Order $order): void
    {
        Log::debug('Order created: ' . $order->id, ['order' => $order]);
        ChargeController::MakePayments($order);

        Log::debug('Order created: ' . $order->id, ['order' => $order]);

        OrderCreate::dispatch(Digital::class, 'saveOrder', $order);

        OrderCreate::dispatch(GiftCard::class, 'saveFromOrder', $order);

        OrderCreate::dispatch(GiftRecord::class, 'saveFromOrder', $order);

        OrderCreate::dispatch(Membership::class, 'updatePoints', $order);

        OrderCreate::dispatch(TransactionController::class, 'AddTransaction', $order);

        OrderCreate::dispatch(AssociatedPurchase::class, 'saveAssociation', $order);

        OrderCreate::dispatch(UtmController::class, 'addSources', $order);

        OrderCreate::dispatch(InventoryController::class, 'DeductFromInventory', $order);

        OrderCreate::dispatch(GiftNotesController::class, 'CreateGiftNotes', $order);

        OrderCreate::dispatch(CampaignMonitorController::class, 'AddToSubscribers', $order);

        OrderCreate::dispatch(PosController::class, 'CreateOrder', $order);

        OrderCreate::dispatch(PosOnlineController::class, 'CreateOrder', $order);

        OrderCreate::dispatch(EmailsController::class, 'SendOrderConfirmation', $order);

        // Send Klaviyo "Placed Order" event
        try {
            if (config('klaviyo.enabled')) {
                $products = collect($order->products ?? []);

                $extractCategories = function ($str) {
                    if (!$str) return [];
                    return collect(explode(',', $str))->map(fn($s) => trim($s))->filter()->values()->all();
                };

                $categories = $products->map(function ($p) use ($extractCategories) {
                    return $extractCategories(data_get($p, 'categories_string'));
                })->flatten()->filter()->unique()->values()->all();

                $itemNames = $products->pluck('title')->filter()->values()->all();
                $brands = $products->pluck('vendor')->filter()->unique()->values()->all();

                $billing = (array) data_get($order, 'payments.creditInfo', []);
                $shipping = (array) data_get($order, 'shipping.shippingInfo', []);

                $splitName = function ($name) {
                    $first = null; $last = null;
                    if ($name) {
                        $parts = preg_split('/\s+/', trim($name));
                        $first = $parts[0] ?? null;
                        $last = count($parts) > 1 ? implode(' ', array_slice($parts, 1)) : null;
                    }
                    return [$first, $last];
                };

                [$billFirst, $billLast] = $splitName($billing['name'] ?? null);
                [$shipFirst, $shipLast] = $splitName($shipping['name'] ?? null);

                $items = $products->map(function ($p) use ($extractCategories) {
                    $qty = (int) data_get($p, 'quantity', 0);
                    $price = (float) data_get($p, 'price', 0);
                    $path = data_get($p, 'path');
                    $url = $path ? url($path) : null;
                    return [
                        'ProductID' => data_get($p, 'product_id') ?? data_get($p, 'id'),
                        'SKU' => data_get($p, 'sku'),
                        'ProductName' => data_get($p, 'title'),
                        'Quantity' => $qty,
                        'ItemPrice' => $price,
                        'RowTotal' => round($price * $qty, 2),
                        'ProductURL' => $url,
                        'ImageURL' => data_get($p, 'media'),
                        'Categories' => $extractCategories(data_get($p, 'categories_string')),
                        'Brand' => data_get($p, 'vendor'),
                    ];
                })->values()->all();

                $properties = [
                    'OrderId' => $order->id,
                    'Categories' => $categories,
                    'ItemNames' => $itemNames,
                    'DiscountCode' => data_get($order, 'discount.name'),
                    'DiscountValue' => (float) ($order->discount_amount ?? 0),
                    'Brands' => $brands,
                    'time' => optional($order->created_at)->toIso8601String(),
                    'value' => (float) $order->grand_total,
                    'value_currency' => 'USD',
                    'BillingAddress' => [
                        'FirstName' => $billFirst,
                        'LastName' => $billLast,
                        'Address1' => $billing['address_line_1'] ?? null,
                        'City' => $billing['city'] ?? null,
                        'RegionCode' => $billing['state'] ?? null,
                        'CountryCode' => $billing['country'] ?? null,
                        'Zip' => $billing['postal_code'] ?? null,
                        'Phone' => $billing['phone'] ?? ($shipping['phone'] ?? null),
                    ],
                    'ShippingAddress' => [
                        'FirstName' => $shipFirst,
                        'LastName' => $shipLast,
                        'Address1' => $shipping['address_line_1'] ?? null,
                        'City' => $shipping['city'] ?? null,
                        'RegionCode' => $shipping['state'] ?? null,
                        'CountryCode' => $shipping['country'] ?? null,
                        'Zip' => $shipping['postal_code'] ?? null,
                        'Phone' => $shipping['phone'] ?? null,
                    ],
                    'Items' => $items,
                ];

                $email = $order->email ?? ($shipping['email'] ?? null);
                if ($email) {
                    $klaviyo = new KlaviyoService();
                    $event = new CreateKlaviyoEventDTO(
                        metric_name: 'Placed Order',
                        profile_email: $email,
                        properties: $properties,
                    );
                    $klaviyo->createEvent($event);
                }
            }
        } catch (\Throwable $e) {
            Log::error('Error sending Klaviyo order event', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function saving($order)
    {
        OrderCreate::dispatch(RouteForMeController::class, 'verifyAddress', $order);
    }
}
