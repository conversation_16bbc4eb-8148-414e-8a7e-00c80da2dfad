<?php

namespace App\Observers;

use App\AssociatedPurchase;
use App\Digital;
use App\GiftCard;
use App\GiftRecord;
use App\Http\Controllers\Api\EmailsController;
use App\Http\Controllers\CampaignMonitorController;
use App\Http\Controllers\GiftNotesController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\PosController;
use App\Http\Controllers\PosOnlineController;
use App\Http\Controllers\RouteForMeController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\UtmController;
use App\Membership;
use App\Order;
use App\Jobs\OrderCreate;
use App\Http\Controllers\ChargeController;
use App\Jobs\SyncKlaviyoOrder;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Support\Facades\Log;

class OrderObserver implements ShouldHandleEventsAfterCommit
{
    public function created(Order $order): void
    {
        Log::debug('Order created: ' . $order->id, ['order' => $order]);
        ChargeController::MakePayments($order);

        Log::debug('Order created: ' . $order->id, ['order' => $order]);

        OrderCreate::dispatch(Digital::class, 'saveOrder', $order);

        OrderCreate::dispatch(GiftCard::class, 'saveFromOrder', $order);

        OrderCreate::dispatch(GiftRecord::class, 'saveFromOrder', $order);

        OrderCreate::dispatch(Membership::class, 'updatePoints', $order);

        OrderCreate::dispatch(TransactionController::class, 'AddTransaction', $order);

        OrderCreate::dispatch(AssociatedPurchase::class, 'saveAssociation', $order);

        OrderCreate::dispatch(UtmController::class, 'addSources', $order);

        OrderCreate::dispatch(InventoryController::class, 'DeductFromInventory', $order);

        OrderCreate::dispatch(GiftNotesController::class, 'CreateGiftNotes', $order);

        OrderCreate::dispatch(CampaignMonitorController::class, 'AddToSubscribers', $order);

        OrderCreate::dispatch(PosController::class, 'CreateOrder', $order);

        OrderCreate::dispatch(PosOnlineController::class, 'CreateOrder', $order);

        OrderCreate::dispatch(EmailsController::class, 'SendOrderConfirmation', $order);

        // Dispatch Klaviyo order sync job (async)
        try {
            if (config('klaviyo.enabled')) {
                SyncKlaviyoOrder::dispatch($order);
            }
        } catch (\Throwable $e) {
            Log::error('Error sending Klaviyo order event', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function saving($order)
    {
        OrderCreate::dispatch(RouteForMeController::class, 'verifyAddress', $order);
    }
}
