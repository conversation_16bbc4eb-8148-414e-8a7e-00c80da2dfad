<?php

declare(strict_types=1);

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * WishListItem Model
 *
 * Represents an individual item within a customer's wishlist.
 *
 * @property int $id
 * @property int $wish_list_id
 * @property int $product_id
 * @property int|null $variation_info_id
 * @property int $quantity
 * @property int $order
 * @property string|null $notes
 * @property int $purchase_quantity
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read WishList $wishList
 * @property-read Product $product
 * @property-read VariationInfo|null $variationInfo
 * @property-read float $total_price
 * @property-read string $display_name
 * @property-read string|null $image
 * @property-read float $price
 * @property-read int $available_quantity
 *
 */
class WishListItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'wish_list_id',
        'product_id',
        'variation_info_id',
        'quantity',
        'order',
        'notes',
        'purchase_quantity'
    ];

    protected $casts = [
        'wish_list_id' => 'integer',
        'product_id' => 'integer',
        'variation_info_id' => 'integer',
        'quantity' => 'integer',
        'order' => 'integer'
    ];

    protected $appends = [
        'total_price',
        'display_name',
        'image',
        'price',
        'available_quantity',
    ];

    protected $with = [
        'product',
        'variationInfo',
    ];

    public function wishList(): BelongsTo
    {
        return $this->belongsTo(WishList::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function variationInfo(): BelongsTo
    {
        return $this->belongsTo(VariationInfo::class);
    }

    public function getTotalPriceAttribute(): float
    {
        $item = $this->variationInfo ?? $this->product;
        $price = $item->price ?? 0;

        return (float) ($price * $this->quantity);
    }

    public function getDisplayNameAttribute(): string
    {
        $name = $this->product->title ?? 'Unknown Product';

        if ($this->variationInfo && isset($this->variationInfo->store_title)) {
            $name .= ' - ' . $this->variationInfo->store_title;
        }

        return $name;
    }

    public function getImageAttribute(): ?string
    {
        if ($this->variationInfo) {
            return $this->variationInfo->image ?? null;
        } elseif ($this->product) {
            return $this->product->image ?? null;
        } else {
            return null;
        }
    }

    public function getPriceAttribute(): float
    {
        return $this->variationInfo?->price && $this->variationInfo->price > 0
            ? (float) $this->variationInfo->price
            : (float) $this->product->price;
    }

    public function getAvailableQuantityAttribute(): int
    {
        if ($this->variationInfo) {
            return $this->variationInfo->max ?? 0;
        } elseif ($this->product) {
            return $this->product->max ?? 0;
        } else {
            return 0;
        }
    }
}
