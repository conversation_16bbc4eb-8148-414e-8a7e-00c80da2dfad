<?php

declare(strict_types=1);

namespace App\Services;

use App\Customer;
use App\WishList;
use App\WishListItem;
use App\Product;
use App\Variation;
use App\Address;
use App\Enums\WishListPrivacyLevel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use InvalidArgumentException;

class WishListService
{
    public function getCustomerWishLists(Customer $customer): Collection
    {
        return $customer->wishLists()
            ->with(['items.product', 'items.variationInfo', 'address'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function createWishList(Customer $customer, array $data): WishList
    {
        $this->validateWishListData($data);

        if (isset($data['address_id'])) {
            $this->validateAddressBelongsToCustomer($customer, $data['address_id']);
        }

        $wishListData = [
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'desired_date' => $data['desired_date'] ?? null,
            'address_id' => $data['address_id'] ?? null,
            'privacy_level' => $data['privacy_level'] ?? WishListPrivacyLevel::PRIVATE,
        ];

        return $customer->wishLists()->create($wishListData);
    }

    public function updateWishList(WishList $wishList, array $data): WishList
    {
        $this->validateWishListData($data, false);

        if (isset($data['address_id'])) {
            $this->validateAddressBelongsToCustomer($wishList->customer, $data['address_id']);
        }

        $wishList->update($data);

        return $wishList->fresh(['address']);
    }

    public function canUserAccessWishList(WishList $wishList, ?Customer $customer, ?string $token): bool
    {
        return $customer?->id === $wishList->customer_id ||
            $wishList->privacy_level === WishListPrivacyLevel::PUBLIC ||
            ($wishList->privacy_level === WishListPrivacyLevel::SHARED && $wishList->share_token === $token);
    }

    public function canUserEditWishList(WishList $wishList, ?Customer $customer): bool
    {
        return $customer?->id === $wishList->customer_id;
    }

    private function validateWishListData(array $data, bool $isCreating = true): void
    {
        if ($isCreating && empty($data['name'])) {
            throw new InvalidArgumentException('Name is required');
        }

        if (isset($data['privacy_level']) && !in_array($data['privacy_level'], WishListPrivacyLevel::toArray())) {
            throw new InvalidArgumentException('Invalid privacy level');
        }

        if (isset($data['desired_date']) && strtotime($data['desired_date']) <= time()) {
            throw new InvalidArgumentException('Desired date must be in the future');
        }
    }

    private function validateAddressBelongsToCustomer(Customer $customer, int $addressId): void
    {
        $address = Address::find($addressId);

        if (!$address ||
            $address->model_id !== $customer->id ||
            $address->model_type !== Customer::class) {
            throw new InvalidArgumentException('Invalid address for customer');
        }
    }

    public function getPublicWishLists(array $filters = [], int $page = 1, int $perPage = 10): LengthAwarePaginator
    {
        $query = WishList::public()
            ->with(['items.product', 'items.variationInfo', 'customer']);

        if (!empty($filters['query'])) {
            $query->where('name', 'like', '%' . $filters['query'] . '%');
        }

        if (!empty($filters['sort_by'])) {
            $sortBy = $filters['sort_by'];
            if ($sortBy === 'customer.name') {
                $query->join('customers', 'wish_lists.customer_id', '=', 'customers.id')
                    ->orderBy('customers.name');
            } elseif ($sortBy === 'name') {
                $query->orderBy('wish_lists.name');
            } else {
                $query->orderBy('wish_lists.created_at');
            }
        } else {
            $query->orderBy('wish_lists.created_at', 'desc');
        }

        return $query->paginate($perPage, ['*'], 'page', $page);
    }
}
