<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Customer;
use App\Http\Controllers\Controller;
use App\Http\Requests\CreateWishListRequest;
use App\Http\Requests\GetPublicWishListsRequest;
use App\Http\Requests\UpdateWishListRequest;
use App\Services\WishListService;
use App\WishList;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class WishListController extends Controller
{
    public function __construct(
        private readonly WishListService $wishListService
    ) {}

    public function index(): JsonResponse
    {
        /** @var Customer $customer */
        $customer = Auth::user();

        $wishLists = $this->wishListService->getCustomerWishLists($customer);

        return response()->json($wishLists);
    }

    public function store(CreateWishListRequest $request): JsonResponse
    {
        /** @var Customer $customer */
        $customer = Auth::user();

        try {
            $wishList = $this->wishListService->createWishList(
                $customer,
                $request->validated()
            );

            return response()->json(
                $wishList->load(['address']),
                Response::HTTP_CREATED
            );
        } catch (\InvalidArgumentException $e) {
            return response()->json(
                ['error' => $e->getMessage()],
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }
    }

    public function show(int $id, Request $request): JsonResponse
    {
        $customer = Auth::user();

        $wishList = WishList::with(['items.product', 'items.product.media', 'items.variationInfo', 'address'])
            ->findOrFail($id);

        $token = $request->query('token');

        if (!$this->wishListService->canUserAccessWishList($wishList, $customer, $token)) {
            return response()->json(
                ['error' => 'Access denied'],
                Response::HTTP_FORBIDDEN
            );
        }

        return response()->json($wishList);
    }

    public function update(UpdateWishListRequest $request, int $id): JsonResponse
    {
        /** @var Customer $customer */
        $customer = Auth::user();

        $wishList = WishList::findOrFail($id);

        if (!$this->wishListService->canUserEditWishList($wishList, $customer)) {
            return response()->json(
                ['error' => 'Access denied'],
                Response::HTTP_FORBIDDEN
            );
        }

        try {
            $updatedWishList = $this->wishListService->updateWishList(
                $wishList,
                $request->validated()
            );

            return response()->json($updatedWishList);
        } catch (\InvalidArgumentException $e) {
            return response()->json(
                ['error' => $e->getMessage()],
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }
    }

    public function destroy(int $id): JsonResponse
    {
        /** @var Customer $customer */
        $customer = Auth::user();

        $wishList = WishList::findOrFail($id);

        if (!$this->wishListService->canUserEditWishList($wishList, $customer)) {
            return response()->json(
                ['error' => 'Access denied'],
                Response::HTTP_FORBIDDEN
            );
        }

        $wishList->delete();

        return response()->json(['message' => 'Wishlist deleted successfully']);
    }

    public function getPublicWishLists(GetPublicWishListsRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $filters = [
            'sort_by' => $validated['sort_by'] ?? 'created_at',
            'query' => $validated['query'] ?? null,
        ];
        $page = $validated['page'] ? intval($validated['page']) : 1;
        $perPage = $validated['per_page'] ? intval($validated['per_page']) : 10;
        $wishLists = $this->wishListService->getPublicWishLists($filters, $page, $perPage);

        return response()->json($wishLists);
    }

}
