<?php

namespace App\Http\Controllers\Api;

use App\Customer;
use App\Http\Controllers\CampaignMonitorController;
use App\Http\Controllers\Controller;
use App\Traits\CustomerBagTrait;
use App\Traits\CustomerFavoriteTrait;
use Hash;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class CustomersController extends Controller
{
    use CustomerBagTrait;
    use CustomerFavoriteTrait;

    public function __construct(Request $request)
    {
        if (!auth()->guard('api')->check()) {
            // return redirect('login');
        }
        //$this->middleware('auth')->except(['create', 'exists']);
    }

    public function exists(Request $request)
    {
        return response()->json([
            'exists' => Customer::where('password', '!=', '')->where('email', $request->email)->exists(),
        ]);
    }

    public function index()
    {
        if (auth()->guard('api')->check()) {
            return;
        }
        abort(403);
    }

    public function createAddress()
    {
        $recieve_promotions = request()->receivePromotions ?? false;
        $address = auth()->guard('api')->user()->addresses()->create(request()->except('receivePromotions'));
        if ($recieve_promotions) {
            CampaignMonitorController::subscribeCustomer(auth()->user(), 'Customer');
        }
        return [
            'status' => 200,
            'message' => 'Address was created.',
            'addresses' => auth()->guard('api')->user()->fresh()->addresses,
            'address' => $address,
        ];
    }

    public function create(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|min:3',
            'email' => 'required|email|unique:customers,email',
            'password' => 'required|min:8|confirmed',
        ]);
        $data['password'] = Hash::make($data['password']);
        auth()->guard('api')->login(Customer::create($data));
        return auth()->guard('api')->user()->load(['payments', 'addresses']);
    }

    public function updateAddress($id)
    {
        $address = auth()->guard('api')->user()->addresses()->findOrFail($id);
        $address->update(request()->all());
        return [
            'status' => 200,
            'message' => 'Address was updated.',
            'addresses' => auth()->guard('api')->user()->fresh()->addresses,
            'address' => $address,
        ];
    }

    public function update(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|min:3',
            'email' => 'email|unique:customers,email,' . auth()->user()->id,
            'phone' => 'required',
        ]);
        $pass_fields = $request->validate([
            'old_password' => 'sometimes',
            'password' => 'required_with:old_password|min:8|confirmed',
        ]);
        if (!empty($pass_fields)) {
            if (Hash::check($pass_fields['old_password'], auth()->user()->password)) {
                $data['password'] = Hash::make($pass_fields['password']);
            } else {
                return response()->json([
                    'errors' => [
                        'old_password' => 'The old password doesnt match.'
                    ],
                    'message' => 'The given data was invalid.'
                ], 422);
            }
        }
        return tap(auth()->guard('api')->user())->update($data);
    }

    public function deleteAddress($id)
    {
        $address = auth()->guard('api')->user()->addresses()->findOrFail($id);
        $address->delete();
        return [
            'status' => 200,
            'message' => 'Address was deleted.',
            'address' => $address
        ];
    }

    public function createPayment()
    {
        $payment = auth()->guard('api')->user()->createPayment(request()->all());
        return [
            'status' => 200,
            'message' => 'Payment was created.',
            'payments' => auth()->guard('api')->user()->fresh()->payments,
            'payment' => $payment
        ];
    }

    public function updatePayment($id)
    {
        $payment = auth()->guard('api')->user()->payments()->findOrFail($id);
        $payment->update(request()->all());
        return [
            'status' => 200,
            'message' => 'Payment was updated.',
            'payments' => auth()->guard('api')->user()->fresh()->payments,
            'payment' => $payment,
        ];
    }

    public function deletePayment($id)
    {
        $payment = auth()->guard('api')->user()->payments()->findOrFail($id);
        $payment->delete();
        return [
            'status' => 200,
            'message' => 'Payment was deleted.',
            // 'payment' => $payment
        ];
    }

    public function digital()
    {
        if (!$user = auth()->guard('api')->user()) {
            abort(403);
        }
        return $user->digitals->map(function ($digital) {
            if ($digital->model) {
                return $digital->model->getFrontEndAttribute();
            }
        })->filter()->values();
    }

    public function getPointsAndStatus()
    {
        $user = auth()->guard('api')->user();
        if (optional($user)->hasActiveMembership) {
            $status = $user->membership->getStatus();
            return array_merge($user->calculatePoints(), $status);
        } else {
            abort(403);
        }
    }

    public function getAddresses(): Collection
    {
        if (!$customer = auth()->guard('api')->user()) {
            abort(403);
        }
        return $customer->addresses;
    }
}
