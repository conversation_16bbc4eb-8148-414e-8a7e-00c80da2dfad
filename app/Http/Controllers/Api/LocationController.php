<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\ZipCode;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use TaxJar\Client;

class LocationController extends Controller
{
    public function getZipCode(Request $request)
    {
        if (session()->get('zip_code')) {
            return session()->get('zip_code');
        }
        $ip = $request->ip();
        $api_key = env('ZIP_API');
        $api_url = 'https://geo.ipify.org/api/v1';

        if(app()->environment(['local'])) {
            return response()
                ->json([
                    'zip' => '10001',
                    'country' => 'US',
                    'tax_rate' => $this->getTaxRate('US', '10001'),
                ]);
        }

        return Cache::remember("location_{$ip}", 60 * 60 * 24 * 7, function () use ($ip, $api_key, $api_url) {
            $results = file_get_contents("{$api_url}?apiKey={$api_key}&ipAddress={$ip}");
            $address = json_decode($results, true);
            $zip = $address['location']['postalCode'];
            $country = $address['location']['country'];

            session()->put('zip_code', [
                'zip' => $zip,
                'country' => $country,
                'tax_rate' => $this->getTaxRate($country, $zip),
            ]);

            return response()
                ->json(session()->get('zip_code'));
        });
    }

    public function getTaxRate($country, $zip)
    {
        $zip_code = ZipCode::getCode($zip);
        if ($country == 'US' && $zip_code && $zip_code->tax_rate) {
            return $zip_code->tax_rate;
        }

        try {
            $client = Client::withApiKey(env('TAX_JAR'));
            $client->setApiConfig('headers', [
                'x-api-version' => '2022-01-24'
            ]);
            $data = $client->ratesForLocation($zip, ['country' => $country]);

            if ($country == 'US' && $zip_code) {
                $zip_code->update([
                    'tax_rate' => $data->combined_rate ?? $data->standard_rate
                ]);
            }

            return $data->combined_rate ?? $data->standard_rate;
        } catch (Exception $ex) {
            return 0;
        }
    }

    public function SetLocationAndGetTaxRate(Request $request)
    {
        session()->put('zip_code', [
            'zip' => $request->zipCode,
            'country' => $request->country,
            'tax_rate' => $this->getTaxRate($request->country, $request->zipCode),
        ]);
        return session()->get('zip_code');
    }
}
