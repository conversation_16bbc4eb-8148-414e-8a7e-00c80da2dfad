<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateAddressRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'street' => ['required', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'state' => ['required', 'string', 'max:255'],
            'zip' => ['required', 'string', 'max:20'],
            'country' => ['required', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Address name is required.',
            'street.required' => 'Street address is required.',
            'city.required' => 'City is required.',
            'state.required' => 'State is required.',
            'zip.required' => 'ZIP code is required.',
            'country.required' => 'Country is required.',
            'name.max' => 'Address name cannot exceed 255 characters.',
            'street.max' => 'Street address cannot exceed 255 characters.',
            'city.max' => 'City cannot exceed 255 characters.',
            'state.max' => 'State cannot exceed 255 characters.',
            'zip.max' => 'ZIP code cannot exceed 20 characters.',
            'country.max' => 'Country cannot exceed 255 characters.',
            'phone.max' => 'Phone number cannot exceed 20 characters.',
        ];
    }
}
