<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Enums\WishListPrivacyLevel;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateWishListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'desired_date' => ['nullable', 'date', 'after:today'],
            'address_id' => ['nullable', 'integer', 'exists:addresses,id'],
            'privacy_level' => [
                'required',
                Rule::in(WishListPrivacyLevel::values())
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Wishlist name is required.',
            'name.max' => 'Wishlist name cannot exceed 255 characters.',
            'description.max' => 'Description cannot exceed 1000 characters.',
            'desired_date.after' => 'Desired date must be in the future.',
            'address_id.exists' => 'Selected address does not exist.',
            'privacy_level.required' => 'Privacy level is required.',
        ];
    }
}
