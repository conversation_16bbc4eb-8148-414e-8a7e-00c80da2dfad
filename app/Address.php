<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Address extends Model
{
    protected $fillable = [
        'address_line_1',
        'address_line_2',
        'city',
        'country',
        'default',
        'model_id',
        'model_type',
        'name',
        'phone',
        'postal_code',
        'state',
    ];

    protected $appends = ['full_address'];

    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    protected static function boot(): void
    {
        parent::boot();

        static::saved(function ($address) {
            if ($address->default) {
                $address->model->addresses()->whereNotIn('id', [$address->id])->update(['default' => false]);
            }
        });
    }

    public function getFullAddressAttribute(): string
    {
        $addressParts = [
            $this->address_line_1,
            $this->address_line_2,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ];

        // Filter out empty parts and join with a comma
        return implode(', ', array_filter($addressParts));
    }
}
