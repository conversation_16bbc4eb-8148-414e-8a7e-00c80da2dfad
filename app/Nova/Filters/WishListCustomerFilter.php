<?php

namespace App\Nova\Filters;

use App\Customer;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use <PERSON>vel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class WishListCustomerFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('customer_id', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function options(NovaRequest $request): array
    {
        return Customer::orderBy('name')
            ->pluck('id', 'name')
            ->toArray();
    }

    /**
     * Get the displayable name of the filter.
     *
     * @return string
     */
    public function name()
    {
        return 'Customer';
    }
}
