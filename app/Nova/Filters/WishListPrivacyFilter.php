<?php

declare(strict_types=1);

namespace App\Nova\Filters;

use App\Enums\WishListPrivacyLevel;
use Illuminate\Database\Eloquent\Builder;
use <PERSON><PERSON>\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class WishListPrivacyFilter extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param NovaRequest $request
     * @param Builder $query
     * @param  mixed  $value
     * @return Builder
     */
    public function apply(NovaRequest $request, $query, $value): Builder
    {
        return match ($value) {
            WishListPrivacyLevel::PUBLIC->value => $query->where('privacy_level', WishListPrivacyLevel::PUBLIC->value),
            WishListPrivacyLevel::SHARED->value => $query->where('privacy_level', WishListPrivacyLevel::SHARED->value),
            WishListPrivacyLevel::PRIVATE->value => $query->where('privacy_level', WishListPrivacyLevel::PRIVATE->value),
            default => $query,
        };
    }

    /**
     * Get the filter's available options.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function options(NovaRequest $request): array
    {
        return WishListPrivacyLevel::toArray();
    }

    /**
     * Get the displayable name of the filter.
     *
     * @return string
     */
    public function name(): string
    {
        return 'Privacy Level';
    }
}
