<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;

class VariationInfo extends Resource
{

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\VariationInfo>
     */
    public static string $model = \App\VariationInfo::class;

    /**
     * Whether the resource should be displayed in the navigation menu.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'sku',
        'barcode',
        'gtin',
        'store_title',
    ];

    /**
     * Get the displayable label of the resource.
     */
    public static function label(): string
    {
        return 'Variation Info';
    }

    /**
     * Get the displayable singular label of the resource.
     */
    public static function singularLabel(): string
    {
        return 'Variation Info';
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Product')
                ->sortable()
                ->searchable()
                ->rules('required')
                ->help('The product this variation belongs to'),

            Text::make('Store Title')
                ->sortable()
                ->rules('nullable', 'max:255')
                ->help('Title used in store displays'),

            Text::make('SKU')
                ->sortable()
                ->rules('nullable', 'max:100')
                ->help('Stock Keeping Unit identifier'),

            Text::make('Barcode')
                ->hideFromIndex()
                ->rules('nullable', 'max:100')
                ->help('Product barcode'),

            Text::make('GTIN')
                ->hideFromIndex()
                ->rules('nullable', 'max:100')
                ->help('Global Trade Item Number'),

            Currency::make('List Price')
                ->currency('USD')
                ->sortable()
                ->rules('nullable', 'numeric', 'min:0')
                ->help('Manufacturer suggested retail price'),

            Currency::make('Store Price')
                ->currency('USD')
                ->sortable()
                ->rules('nullable', 'numeric', 'min:0')
                ->help('Price in physical store'),

            Currency::make('Online Price')
                ->currency('USD')
                ->sortable()
                ->rules('nullable', 'numeric', 'min:0')
                ->help('Price for online sales'),

            Currency::make('Sale Price')
                ->currency('USD')
                ->hideFromIndex()
                ->rules('nullable', 'numeric', 'min:0')
                ->help('Discounted sale price'),

            Currency::make('Cost Price')
                ->currency('USD')
                ->hideFromIndex()
                ->rules('nullable', 'numeric', 'min:0')
                ->help('Cost to acquire this item'),

            Number::make('Store Quantity')
                ->sortable()
                ->rules('nullable', 'integer', 'min:0')
                ->help('Quantity available in store'),

            Number::make('Website Quantity')
                ->sortable()
                ->rules('nullable', 'integer', 'min:0')
                ->help('Quantity available for online sales'),

            Number::make('Max Quantity')
                ->hideFromIndex()
                ->rules('nullable', 'integer', 'min:0')
                ->help('Maximum quantity that can be ordered'),

            Boolean::make('Track Inventory')
                ->sortable()
                ->help('Whether to track inventory for this variation'),

            Boolean::make('Visibility')
                ->sortable()
                ->help('Whether this variation is visible to customers'),

            Select::make('Item Type')
                ->options([
                    'physical' => 'Physical',
                    'digital' => 'Digital',
                    'service' => 'Service',
                ])
                ->displayUsingLabels()
                ->sortable()
                ->rules('nullable', 'in:physical,digital,service')
                ->help('Type of item'),

            Number::make('Width')
                ->hideFromIndex()
                ->step(0.01)
                ->rules('nullable', 'numeric', 'min:0')
                ->help('Width in inches'),

            Number::make('Height')
                ->hideFromIndex()
                ->step(0.01)
                ->rules('nullable', 'numeric', 'min:0')
                ->help('Height in inches'),

            Number::make('Length')
                ->hideFromIndex()
                ->step(0.01)
                ->rules('nullable', 'numeric', 'min:0')
                ->help('Length in inches'),

            Number::make('Weight')
                ->hideFromIndex()
                ->step(0.01)
                ->rules('nullable', 'numeric', 'min:0')
                ->help('Weight in pounds'),

            Number::make('Boxes')
                ->hideFromIndex()
                ->rules('nullable', 'integer', 'min:1')
                ->help('Number of boxes for shipping'),

            Text::make('Origin')
                ->hideFromIndex()
                ->rules('nullable', 'max:100')
                ->help('Country or region of origin'),

            Text::make('System Code')
                ->hideFromIndex()
                ->rules('nullable', 'max:100')
                ->help('Internal system identifier'),

            Text::make('Store Vendor')
                ->hideFromIndex()
                ->rules('nullable', 'max:255')
                ->help('Vendor for store sales'),

            Text::make('Store Category')
                ->hideFromIndex()
                ->rules('nullable', 'max:255')
                ->help('Category for store organization'),

            Text::make('Store Sub Category')
                ->hideFromIndex()
                ->rules('nullable', 'max:255')
                ->help('Sub-category for store organization'),

            Images::make('Images', 'default')
                ->hideFromIndex()
                ->help('Product variation images'),

            Code::make('Product JSON')
                ->onlyOnDetail()
                ->help('Additional product data in JSON format'),

            Textarea::make('Meta')
                ->onlyOnDetail()
                ->rules('nullable')
                ->help('Additional metadata'),

            Text::make('Created At')
                ->displayUsing(function ($value) {
                    return $value ? $value->format('M j, Y g:i A') : '';
                })
                ->onlyOnDetail(),

            Text::make('Updated At')
                ->displayUsing(function ($value) {
                    return $value ? $value->format('M j, Y g:i A') : '';
                })
                ->onlyOnDetail(),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Build an "index" query for the given resource.
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->with(['product', 'sale']);
    }

    /**
     * Get the value that should be displayed to represent the resource.
     */
    public function title()
    {
        $title = $this->store_title ?: "Variation #{$this->id}";
        if ($this->sku) {
            $title .= " ({$this->sku})";
        }
        return $title;
    }

    /**
     * Get the search result subtitle for the resource.
     */
    public function subtitle()
    {
        return "Product: {$this->product->title}";
    }
}
