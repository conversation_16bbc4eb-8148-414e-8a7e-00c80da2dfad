<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\Color;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Label extends Resource
{
    use ProductLister;

    public static string $model = \App\Label::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'color',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()
                ->sortable(),

            Text::make('Name')
                ->sortable(),

            Color::make('Color'),
        ];
    }
}
