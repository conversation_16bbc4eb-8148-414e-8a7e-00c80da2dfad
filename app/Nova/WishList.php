<?php

declare(strict_types=1);

namespace App\Nova;

use App\Enums\WishListPrivacyLevel;
use Illuminate\Database\Eloquent\Builder;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON><PERSON>\Nova\Fields\FormData;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Fields\Badge;
use Laravel\Nova\Fields\Number;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use NovaAttachMany\AttachMany;


class WishList extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\WishList>
     */
    public static string $model = \App\WishList::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'description',
    ];

    /**
     * Get the displayable label of the resource.
     */
    public static function label(): string
    {
        return 'Wish Lists';
    }

    /**
     * Get the displayable singular label of the resource.
     */
    public static function singularLabel(): string
    {
        return 'Wish List';
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255')
                ->help('The name of the wish list'),

            Textarea::make('Description')
                ->hideFromIndex()
                ->rules('nullable', 'max:1000')
                ->help('Optional description of the wish list'),

            BelongsTo::make('Customer')
                ->sortable()
                ->searchable()
                ->rules('required')
                ->help('The customer who owns this wish list'),

            Select::make('Privacy Level', 'privacy_level')
                ->options(WishListPrivacyLevel::toNovaOptions())
                ->rules('required')
                ->help('The visibility of this wish list')
                ->onlyOnForms()
                ->displayUsingLabels(),

            BelongsTo::make('Address')
                ->nullable()
                ->hideFromIndex()
                ->help('Delivery address for this wish list')
                ->dependsOn('customer', function (BelongsTo $field, NovaRequest $request, FormData $formData) {
                    $field->relatableQueryUsing(function (NovaRequest $request, Builder $query) use ($formData) {
                        $customer = $request->get('customer') ?: $formData->get('customer');
                        return $query->where('model_type', \App\Customer::class)
                            ->where('model_id', $customer ? (int)$customer : 0);
                    });
                }),

            Date::make('Desired Date')
                ->nullable()
                ->hideFromIndex()
                ->help('When the customer would like to receive these items'),

            Badge::make('Privacy Level')->map([
                WishListPrivacyLevel::PUBLIC->value => 'success',
                WishListPrivacyLevel::SHARED->value => 'warning',
                WishListPrivacyLevel::PRIVATE->value => 'danger',
            ])->sortable(),

            Text::make('Share Token')
                ->onlyOnDetail()
                ->canSee(function () {
                    return $this->resource->is_shared && $this->resource->share_token;
                })
                ->help('Token used for sharing this wish list'),

            Number::make('Items Count')
                ->onlyOnIndex()
                ->sortable()
                ->help('Number of items in this wish list'),

            HasMany::make('Items','items', WishListItem::class),

            Text::make('Created At')
                ->displayUsing(function ($value) {
                    return $value ? $value->format('M j, Y g:i A') : '';
                })
                ->onlyOnDetail(),

            Text::make('Updated At')
                ->displayUsing(function ($value) {
                    return $value ? $value->format('M j, Y g:i A') : '';
                })
                ->onlyOnDetail(),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [
            new Filters\WishListPrivacyFilter,
            new Filters\WishListCustomerFilter,
        ];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
