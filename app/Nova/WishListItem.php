<?php

namespace App\Nova;

use Illuminate\Database\Eloquent\Builder;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\FormData;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class WishListItem extends Resource
{
    public static string $model = \App\WishListItem::class;

    public static $title = 'display_name';

    public static $search = [
        'id',
        'display_name',
        'notes',
    ];

    public static $displayInNavigation = false;

    /**
     * Get the displayable label of the resource.
     */
    public static function label(): string
    {
        return 'Wish List Items';
    }

    /**
     * Get the displayable singular label of the resource.
     */
    public static function singularLabel(): string
    {
        return 'Wish List Item';
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Product')
                ->sortable()
                ->searchable()
                ->rules('required')
                ->help('The product being wished for'),

            BelongsTo::make('VariationInfo', 'variationInfo', VariationInfo::class)
                ->nullable()
                ->hideFromIndex()
                ->help('Specific variation of the product (if applicable)')
                ->dependsOn('product', function (BelongsTo $field, NovaRequest $request, FormData $formData) {
                    $field->relatableQueryUsing(function (NovaRequest $request, Builder $query) use ($formData) {
                       $product = $request->get('product') ?: $formData->get('product');
                        $query->where('product_id', $product)
                            ->orderBy('store_title');
                    });
                }),

            Number::make('Quantity')
                ->sortable()
                ->rules('required', 'integer', 'min:1')
                ->default(1)
                ->help('How many of this item are desired'),

            Number::make('Order')
                ->sortable()
                ->rules('required', 'integer', 'min:0')
                ->default(0)
                ->help('The order in which this item should appear in the wish list'),

            Textarea::make('Notes')
                ->hideFromIndex()
                ->rules('nullable', 'max:500')
                ->help('Optional notes about this item'),

            Text::make('Created At')
                ->displayUsing(function ($value) {
                    return $value ? $value->format('M j, Y g:i A') : '';
                })
                ->onlyOnDetail(),

            Text::make('Updated At')
                ->displayUsing(function ($value) {
                    return $value ? $value->format('M j, Y g:i A') : '';
                })
                ->onlyOnDetail(),
        ];
    }

    /**
     * Build an "index" query for the given resource.
     */
    public static function indexQuery(NovaRequest $request, $query): Builder
    {
        return $query->with(['wishList', 'product', 'variationInfo']);
    }
}
