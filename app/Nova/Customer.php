<?php

namespace App\Nova;

use <PERSON><PERSON><PERSON>\DependencyContainer\HasDependencies;
use App\Enums\TaxExemptType;
use App\Nova\Metrics\Customers;
use App\Nova\Metrics\CustomersByState;
use Ebess\AdvancedNovaMediaLibrary\Fields\Files;
use Illuminate\Support\Facades\DB;
use <PERSON>vel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\MorphMany;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Customer extends Resource
{
    use OrderFulfiller, HasDependencies;

    public static string $model = \App\Customer::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'email',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make('Email')
                ->rules('required', 'email', 'max:254')
                ->creationRules('unique:customers,email')
                ->updateRules('unique:customers,email,{{resourceId}}'),

            MorphMany::make('Addresses'),

            Text::make('Phone Number')
                ->onlyOnIndex(),

            BelongsTo::make('Group')->nullable(),

            Select::make('Tax Exempt')
                ->options(TaxExemptType::toNovaOptions())->nullable()->withMeta(["value" => $this->tax_exempt ?? null]),

            Select::make('Subscription Status')->options($this->subscriptionOptions)
                ->nullable()
                ->hideFromIndex()
                ->help('May take up to a few minutes to take effect..'),

            Files::make('Tax Exempt Proof', 'file_upload'),

            Boolean::make('Has Account', 'password')->onlyOnDetail(),

            Heading::make('Utm Details')->onlyOnDetail(),

            Text::make('Source', 'id')->resolveUsing(function ($id) {
                return data_get(
                    DB::table('utm')->where('model_type', 'App\\Customer')->where('model_id', $id)
                        ->select('utm_source')->first(),
                    'utm_source'
                );
            })->onlyOnDetail(),

            Text::make('Medium', 'id')->resolveUsing(function ($id) {
                return data_get(
                    DB::table('utm')->where('model_type', 'App\\Customer')->where('model_id', $id)
                        ->select('utm_medium')->first(),
                    'utm_medium'
                );
            })->onlyOnDetail(),

            Text::make('Campaign', 'id')->resolveUsing(function ($id) {
                return data_get(
                    DB::table('utm')->where('model_type', 'App\\Customer')->where('model_id', $id)
                        ->select('utm_campaign')->first(),
                    'utm_campaign'
                );
            })->onlyOnDetail(),

            Text::make('Term', 'id')->resolveUsing(function ($id) {
                return data_get(
                    DB::table('utm')->where('model_type', 'App\\Customer')->where('model_id', $id)
                        ->select('utm_term')->first(),
                    'utm_term'
                );
            })->onlyOnDetail(),

            Text::make('Content', 'id')->resolveUsing(function ($id) {
                return data_get(
                    DB::table('utm')->where('model_type', 'App\\Customer')->where('model_id', $id)
                        ->select('utm_content')->first(),
                    'utm_content'
                );
            })->onlyOnDetail(),


            HasMany::make('Bag'),
            HasMany::make('Orders'),
            HasMany::make('Wish Lists', 'wishLists', WishList::class),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [
            new CustomersByState,
            new Customers,
        ];
    }
}
