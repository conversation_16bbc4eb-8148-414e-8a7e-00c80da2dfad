<template>
    <div class="container">
        <div>
            <h1>Wish Lists</h1>

            <div class="wishLists-filters">
                <custom-input
                    class="wishLists-search"
                    v-model="query"
                    placeholder="Search Wish Lists"
                    @keyup.enter="searchWishLists"
                />

            </div>

            <div v-if="isLoading" class="loader">
                <inline-loader />
            </div>
            <div v-else class="wishLists-list">
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Customer</th>
                            <th>Created At</th>
                            <th>Items Count</th>
                            <th>Purchased Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="wishList in wishLists" :key="wishList.id">
                            <td>
                                <a :href="wishList.url" target="_blank">
                                    {{ wishList.name }}
                                </a>
                            </td>
                            <td>{{ wishList.customer.name }}</td>
                            <td>{{ new Date(wishList.created_at).toLocaleDateString() }}</td>
                            <td>{{ wishList.items_count }}</td>
                            <td>{{ wishList.purchased_count }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>

<script>
import WishListService from '../services/WishListService';

export default {
    name: 'WishListsView',
    data() {
        return {
            wishLists: [],
            page: 1,
            lastPage: 1,
            query: '',
            sort_by: 'created_at',
            isLoading: true,
        };
    },
    async mounted() {
        await this.fetchWishLists();
    },
    methods: {
        async fetchWishLists() {
            try {
                this.isLoading = true;
                const filters = {
                    query: this.query,
                    sort_by: this.sort_by,
                };
                const response = await WishListService.getPublicWishLists(filters, this.page);
                if (response && response.data) {
                    this.wishLists = response.data;
                } else {
                    this.wishLists = [];
                }
            } catch (error) {
                console.error('Error fetching wish lists:', error);
            } finally {
                this.isLoading = false;
            }
        },
        async searchWishLists() {
            this.page = 1;
            await this.fetchWishLists();
        },
    },
};
</script>

<style lang="scss" scoped>
.container{
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
    display: block;
    h1{
        text-align: center;
        margin-bottom: 2rem;
    }
}
.loader{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.wishLists-filters{
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    .wishLists-search{
        flex: 1;
        margin-right: 1rem;
    }
    .wishLists-sort{
        width: 200px;
    }
}
.wishLists-list{
    table{
        width: 100%;
        border-collapse: collapse;
        th, td{
            padding: 0.5rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th{
            background-color: #f4f4f4;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
    }
}
</style>
