<template>

    <div class="singleWishList">

        <div v-if="isLoading" class="singleWishList-loader">

            <inline-loader />

        </div>

        <div v-else>

            <div class="singleWishList-header">

                <div class="container">

                    <h1>{{ wishList.name }}</h1>

                    <p v-if="wishList.description">{{ wishList.description }}</p>

                </div>

            </div>

            <div class="singleWishList-info">

                <span v-if="wishList.customer_name">Owner: {{ wishList.customer_name }}</span>

                <span v-if="wishList.desired_date">
                     Desired Date: {{ new Date(wishList.desired_date).toLocaleDateString() }}
                </span>

                <span>Purchased Items: {{ wishList.purchased_count }} / {{ wishList.items_count }}</span>

            </div>

            <div class="singleWishList-products">

                <div class="container">

                    <div class="singleWishList-products-filters">

                        <span>Filter by:</span>

                        <button
                            :class="{ active: filters.stillNeeded }"
                            @click="filters.stillNeeded = !filters.stillNeeded">
                             Still Needed
                        </button>

                        <button :class="{ active: filters.purchased }" @click="filters.purchased = !filters.purchased">
                             Purchased
                        </button>

                    </div>

                    <table>

                        <tbody>

                            <tr v-for="item in items" :key="item.id" class="singleWishList-product">

                                <td>

                                    <div class="singleWishList-product-details">

                                        <img :src="item.image" alt="Product Image" />

                                        <div class="singleWishList-product-info">

                                            <h2>{{ item.display_name }}</h2>

                                            <p v-if="item.notes">{{ item.notes }}</p>

                                        </div>

                                    </div>

                                </td>

                                <td class="product-price">{{ item.price | currency }}</td>

                                <td>

                                    <div class="singleWishList-product-quantity">

                                        <input
                                            type="number"
                                            min="1"
                                            :max="item.available_quantity"
                                            v-model.number="item.quantity" />

                                    </div>

                                </td>

                                <td class="product-price">{{ (item.price * item.quantity) | currency }}</td>

                                <td>

                                    <div
                                        v-if="item.available_quantity <= 0"
                                        class="singleWishList-product-actions text-danger">

                                        <strong>Out of Stock</strong>

                                    </div>

                                    <div
                                        v-else-if="item.purchase_quantity === item.quantity"
                                        class="singleWishList-product-actions text-success">

                                        <strong>Purchased</strong>

                                    </div>

                                    <div v-else class="singleWishList-product-actions">

                                        <button class="add-to-cart" @click="addItemToCart(item)">Add to Cart</button>

                                        <button class="add-to-my-cart">Add to My Cart</button>

                                    </div>

                                </td>

                            </tr>

                        </tbody>

                    </table>

                </div>

            </div>

        </div>

    </div>

</template>

<script>
import WishListService from '../services/WishListService.js';
import {mapActions} from "vuex";
export default {
    name: 'WishListSingleView',
    data: () => {
        return {
            wishList: null,
            isLoading: true,
            filters: {
                purchased: false,
                stillNeeded: true,
            },
        };
    },
    computed: {
        items() {
            if (!this.wishList || !this.wishList.items) return [];
            return this.wishList.items.filter((item) => {
                if (this.filters.purchased && item.purchase_quantity >= item.quantity) {
                    return true;
                }
                if (this.filters.stillNeeded && item.purchase_quantity < item.quantity) {
                    return true;
                }
                return !this.filters.purchased && !this.filters.stillNeeded;
            });
        },
    },
    async mounted() {
        const wishListId = this.$route.params.id;
        const wishListToken = this.$route.query.token || null;
        await this.fetchWishList(wishListId, wishListToken);
    },
    methods: {
        ...mapActions(['addToCart']),
        async fetchWishList(wishListId, wishListToken) {
            try {
                this.isLoading = true;
                this.wishList = await WishListService.getWishListById(wishListId, wishListToken);
            } catch (error) {
                console.error('There has been a problem with your fetch operation:', error);
                await this.$router.push({ name: 'WishLists' });
            } finally {
                this.isLoading = false;
            }
        },
        addItemToCart(item) {
            const cartItem = {
                id: item.id,
                name: item.display_name,
                price: item.price,
                quantity: item.quantity,
                image: item.image,
            };
            this.addToCart(cartItem);
        }
    },
};
</script>

<style lang="scss" scoped>
.singleWishList{
  margin: 4rem 0 5rem;
  .container{
    max-width: 1200px;
    margin: 0 auto;
    display: block;
  }
  &-loader {
        display: flex;
        justify-content: center;
        align-items: center;
  }
  &-header {
        text-align: center;
        margin-bottom: 1rem;
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        p {
            font-size: 1.2rem;
            color: #666;
          margin-bottom: 0.5rem;
        }
    }
    &-info {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--button_and_green_text);
    gap: 3rem;
    span {
        font-size: 1rem;
        color: #fff;
    }
    }
    &-products{
        table{
            border-collapse: collapse;
            width: 100%;
        }
        &-filters{
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            span {
                font-size: 1rem;
                color: #333;
            }
            button {
                padding: 0.5rem 1rem;
                border: none;
                border-radius: 5px;
                background-color: #f0f0f0;
                color: #333;
                cursor: pointer;
                transition: background-color 0.3s ease;
                &.active {
                    background-color: var(--button_and_green_text);
                    color: #fff;
                }
                &:hover {
                    background-color: var(--darkgreen);
                    color: #fff;
                }
            }
        }
    }
    &-product{
    border-bottom: 1px solid #ddd;
    &-details{
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    &-info{
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        h2{
            font-size: 1.2rem;
            font-weight: 500;
        }
    }
    img{
        display: block;
        border-radius: 5px;
        width: 100px;
        height: 100px;
        object-fit: cover;
        background: #eee;
    }
    &-actions{
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
        gap: 0.5rem;
        .add-to-cart{
            padding: 1rem;
            min-width: 150px;
            margin: 0 0.5rem;
            border: none;
            border-radius: 5px;
            background-color: var(--button_and_green_text);
            color: #fff;
            cursor: pointer;
            transition: background-color 0.3s ease;
            &:hover {
                background-color: var(--darkgreen);
            }
        }
        .add-to-my-cart{
            color: #333;
            &:hover {
                color: var(--button_and_green_text);
            }
        }
    }
    &-quantity{
        display: flex;
        align-items: center;
        input{
            width: 60px;
            padding: 0.5rem;
            border: 1px solid #ccc;
            border-radius: 5px;
            text-align: center;
        }
    }
    }

}
</style>

