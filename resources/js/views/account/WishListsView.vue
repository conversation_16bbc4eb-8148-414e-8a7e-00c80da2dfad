<template>

    <div class="user_content_wrapper">

        <div class="usc_center">

            <div class="user_card">

                <div class="uc_head">

                    <p class="uc_head_ttl">Wish Lists</p>

                    <p class="uc_head_btn" @click="addWishList">Add Wish List</p>

                </div>

                <div v-if="!isLoading">

                    <div v-if="wishlists && wishlists.length > 0" class="uc_body uc_card_body">

                        <div class="ucb_card" v-for="wishlist in wishlists" :key="wishlist.id">

                            <span class="ucb_card_label" :class="`ucb_card_privacy_${wishlist.privacy_level}`">
                                 {{ wishlist.privacy_level }}
                            </span>

                            <div class="ucb_card_top">

                                <h3 class="mb_5">{{ wishlist.name }}</h3>

                                <p class="ucb_card_date">

                                    <span>
                                         Desired Date: {{
                                            wishlist.desired_date
                                                ? new Date(wishlist.desired_date).toLocaleDateString()
                                                : 'Not specified'
                                        }}
                                    </span>

                                </p>

                                <p class="ucb_card_address">

                                    <span>
                                         Address: {{
                                            wishlist.address ? wishlist.address.full_address : 'Not specified'
                                        }}
                                    </span>

                                </p>

                            </div>

                            <div class="ucb_card_bot">

                                <a :href="wishlist.url" target="_blank">

                                    <p class="ucb_card_view">View Wish List</p>

                                </a>

                                <div class="ucb_actions">

                                    <p class="ucb_edit" @click="editWishList(wishlist.id)">Edit</p>

                                    <p class="ucb_sep">|</p>

                                    <p class="ucb_del" @click="deleteWishList(wishlist.id)">Delete</p>

                                </div>

                            </div>

                        </div>

                    </div>

                    <div v-else class="uc_body uc_card_body">

                        <div>No wish lists found.</div>

                    </div>

                </div>

                <div v-else class="uc_loading_wrapper">

                    <inline-loader :show-force="true" />

                </div>

            </div>

        </div>

        <modal-wish-list
            :show="showModal"
            :addresses="addresses"
            :id="editWishListId"
            @close="showModal = false"
            @wishListUpdated="fetchWishLists" />

    </div>

</template>

<script>
import WishListService from '@/services/WishListService';
import CustomerService from '@/services/CustomerService';
import ModalWishList from '@/components/account/ModalWishList.vue';

export default {
    name: 'WishListsView',
    components: { ModalWishList },
    data() {
        return {
            isLoading: true,
            wishlists: [],
            addresses: [],
            showModal: false,
            editWishListId: null,
        };
    },
    async mounted() {
        await this.fetchWishLists();
        await this.fetchAddresses();
        this.isLoading = false;
    },
    methods: {
        async fetchWishLists() {
            try {
                this.isLoading = true;
                this.wishlists = await WishListService.getWishLists();
            } catch (error) {
                console.error('Error fetching wish lists:', error);
            } finally {
                this.isLoading = false;
            }
        },
        async fetchAddresses() {
            try {
                this.isLoading = true;
                this.addresses = await CustomerService.getAddresses();
            } catch (error) {
                console.error('Error fetching addresses:', error);
            } finally {
                this.isLoading = false;
            }
        },
        addWishList() {
            this.editWishListId = null;
            this.showModal = true;
        },
        editWishList(id) {
            this.editWishListId = id;
            this.showModal = true;
        },
        async deleteWishList(id) {
            if (confirm('Are you sure you want to delete this wish list?')) {
                try {
                    await WishListService.deleteWishList(id);
                    await this.fetchWishLists();
                } catch (error) {
                    alert('Error deleting wish list.');
                    console.error(error);
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.ucb_card{
        height: 200px;
        @media screen and (max-width: 768px) {
            height: auto;
        }
    }
    .ucb_card_bot{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .ucb_card_bot a{
        padding: 0.5rem 1rem;
        border-radius: 5px;
        margin: 0;
        background: var(--button_and_green_text);
        color: #fff;
    }
</style>

