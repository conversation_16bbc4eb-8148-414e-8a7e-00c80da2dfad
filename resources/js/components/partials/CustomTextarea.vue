<template>

    <span>

        <label :class="{ 'on-error': onError }" class="pure-material-textfield-outlined">

            <textarea
                placeholder=""
                :autocomplete="autocomplete"
                :name="name"
                :id="id"
                :value="value"
                :style="customStyle"
                @blur="$emit('blur', $event)"
                @focus="$emit('focus', $event)"
                @input="$emit('input', $event.target.value.trim())"></textarea>

            <span>{{ placeholder }}</span>

        </label>

        <div class="error-wrp">

            <small v-for="(value, key) in errors" :key="key" v-show="value">{{ key }}</small>

        </div>

    </span>

</template>

<script>
export default {
    props: {
        id: String,
        name: String,
        autocomplete: String,
        value: String,
        placeholder: String,
        errors: Object,
        onError: Boolean,
        customStyle: [String, Object],
    },
};
</script>

<style scoped>
.error-wrp {
    min-height: 16px;
    display: flex;
    flex-direction: column;
}

.error-wrp small {
    color: #f66;
}

.pure-material-textfield-outlined {
    width: 100%;
    position: relative;
    display: inline-block;
    padding-top: 6px;
    font-size: 17px;
    line-height: 1.5;
    /* overflow: hidden; */
    font-weight: 300;
    color: #707070;
}

/* Input, Textarea */
.pure-material-textfield-outlined > input,
.pure-material-textfield-outlined > textarea {
    font-size: 17px;
    font-weight: 300;
    color: #707070;
    resize: vertical;
    max-height: 200px;
    background-color: white;
    box-sizing: border-box;
    margin: 0;
    border: solid 1px; /* Safari */
    border: 1px solid var(--gold_outline);
    border-top-color: transparent;
    padding: 15px 13px 15px;
    width: 100%;
    height: inherit;
    box-shadow: none; /* Firefox */
    line-height: inherit;
    caret-color: #707070;
    transition: border 0.2s, box-shadow 0.2s;
}

.pure-material-textfield-outlined.on-error > input,
.pure-material-textfield-outlined.on-error > textarea {
    border-color: #f66;
    border-top-color: transparent;
}

.pure-material-textfield-outlined.on-error
    > input:not(:focus):placeholder-shown,
.pure-material-textfield-outlined.on-error
    > textarea:not(:focus):placeholder-shown {
    border-top-color: #f66;
}

.pure-material-textfield-outlined.on-error > input + span::before,
.pure-material-textfield-outlined.on-error > input + span::after {
    border-top-color: #f66;
}

/* Span */
.pure-material-textfield-outlined > input + span,
.pure-material-textfield-outlined > textarea + span {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    border-color: var(--gold_outline);
    width: 100%;
    max-height: 100%;
    font-size: 75%;
    line-height: 15px;
    cursor: text;
    transition: color 0.2s, font-size 0.2s, line-height 0.2s;
}

/* Corners */
.pure-material-textfield-outlined > input + span::before,
.pure-material-textfield-outlined > input + span::after,
.pure-material-textfield-outlined > textarea + span::before,
.pure-material-textfield-outlined > textarea + span::after {
    content: '';
    display: block;
    box-sizing: border-box;
    margin-top: 6px;
    border-top: solid 1px;
    border-top-color: var(--gold_outline);
    min-width: 10px;
    height: 8px;
    pointer-events: none;
    box-shadow: inset 0 1px transparent;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.pure-material-textfield-outlined > input + span::before,
.pure-material-textfield-outlined > textarea + span::before {
    margin-right: 4px;
    border-left: solid 1px transparent;
}

.pure-material-textfield-outlined > input + span::after,
.pure-material-textfield-outlined > textarea + span::after {
    flex-grow: 1;
    margin-left: 4px;
    border-right: solid 1px transparent;
}

/* Hover */
.pure-material-textfield-outlined:hover > input,
.pure-material-textfield-outlined:hover > textarea {
    border-color: var(--gold);
    border-top-color: transparent;
}

.pure-material-textfield-outlined:hover > input + span::before,
.pure-material-textfield-outlined:hover > textarea + span::before,
.pure-material-textfield-outlined:hover > input + span::after,
.pure-material-textfield-outlined:hover > textarea + span::after {
    border-top-color: var(--gold);
}

.pure-material-textfield-outlined:hover > input:not(:focus):placeholder-shown,
.pure-material-textfield-outlined:hover
    > textarea:not(:focus):placeholder-shown {
    border-color: var(--gold);
}

/* Placeholder-shown */
.pure-material-textfield-outlined > input:not(:focus):placeholder-shown,
.pure-material-textfield-outlined > textarea:not(:focus):placeholder-shown {
    border-top-color: var(--gold_outline);
}

.pure-material-textfield-outlined > input:not(:focus):placeholder-shown + span,
.pure-material-textfield-outlined
    > textarea:not(:focus):placeholder-shown
    + span {
    font-size: inherit;
    line-height: 68px;
}

.pure-material-textfield-outlined
    > input:not(:focus):placeholder-shown
    + span::before,
.pure-material-textfield-outlined
    > textarea:not(:focus):placeholder-shown
    + span::before,
.pure-material-textfield-outlined
    > input:not(:focus):placeholder-shown
    + span::after,
.pure-material-textfield-outlined
    > textarea:not(:focus):placeholder-shown
    + span::after {
    border-top-color: transparent;
}

/* Focus */
.pure-material-textfield-outlined > input:focus,
.pure-material-textfield-outlined > textarea:focus {
    border-color: var(--gold);
    border-top-color: transparent;
    box-shadow: inset 1px 0 var(--pure-material-safari-helper1),
        inset -1px 0 var(--pure-material-safari-helper1),
        inset 0 -1px var(--pure-material-safari-helper1);
    outline: none;
}

.pure-material-textfield-outlined > input:focus + span,
.pure-material-textfield-outlined > textarea:focus + span {
    color: var(--gold);
}

.pure-material-textfield-outlined > input:focus + span::before,
.pure-material-textfield-outlined > input:focus + span::after,
.pure-material-textfield-outlined > textarea:focus + span::before,
.pure-material-textfield-outlined > textarea:focus + span::after {
    border-top-color: var(--gold) !important;
    box-shadow: inset 0 1px var(--pure-material-safari-helper1);
}

/* Disabled */
/* .pure-material-textfield-outlined > input:disabled,
.pure-material-textfield-outlined > input:disabled + span,
.pure-material-textfield-outlined > textarea:disabled,
.pure-material-textfield-outlined > textarea:disabled + span {
    border-color: rgba(var(--pure-material-onsurface-rgb, 0, 0, 0), 0.38) !important;
    border-top-color: transparent !important;
    color: rgba(var(--pure-material-onsurface-rgb, 0, 0, 0), 0.38);
    pointer-events: none;
}

.pure-material-textfield-outlined > input:disabled + span::before,
.pure-material-textfield-outlined > input:disabled + span::after,
.pure-material-textfield-outlined > textarea:disabled + span::before,
.pure-material-textfield-outlined > textarea:disabled + span::after {
    border-top-color: rgba(var(--pure-material-onsurface-rgb, 0, 0, 0), 0.38) !important;
}

.pure-material-textfield-outlined > input:disabled:placeholder-shown,
.pure-material-textfield-outlined > input:disabled:placeholder-shown + span,
.pure-material-textfield-outlined > textarea:disabled:placeholder-shown,
.pure-material-textfield-outlined > textarea:disabled:placeholder-shown + span {
    border-top-color: rgba(var(--pure-material-onsurface-rgb, 0, 0, 0), 0.38) !important;
}

.pure-material-textfield-outlined > input:disabled:placeholder-shown + span::before,
.pure-material-textfield-outlined > input:disabled:placeholder-shown + span::after,
.pure-material-textfield-outlined > textarea:disabled:placeholder-shown + span::before,
.pure-material-textfield-outlined > textarea:disabled:placeholder-shown + span::after {
    border-top-color: transparent !important;
} */

/* Faster transition in Safari for less noticable fractional font-size issue */
@media not all and (min-resolution: 0.001dpcm) {
    @supports (-webkit-appearance: none) {
        .pure-material-textfield-outlined > input,
        .pure-material-textfield-outlined > input + span,
        .pure-material-textfield-outlined > textarea,
        .pure-material-textfield-outlined > textarea + span,
        .pure-material-textfield-outlined > input + span::before,
        .pure-material-textfield-outlined > input + span::after,
        .pure-material-textfield-outlined > textarea + span::before,
        .pure-material-textfield-outlined > textarea + span::after {
            transition-duration: 0.1s;
        }
    }
}

@media only screen and (max-width: 959px) {
    .pure-material-textfield-outlined {
        font-size: 14px;
    }

    .pure-material-textfield-outlined > input,
    .pure-material-textfield-outlined > textarea {
        font-size: 14px;
        padding: 10px 13px 10px;
    }

    .pure-material-textfield-outlined
        > input:not(:focus):placeholder-shown
        + span,
    .pure-material-textfield-outlined
        > textarea:not(:focus):placeholder-shown
        + span {
        font-size: inherit;
        line-height: 58px;
    }
}
</style>

