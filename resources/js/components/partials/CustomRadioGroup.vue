<template>

    <div class="custom-radio-group">

        <label class="group-label pure-material-label">{{ label }}</label>

        <div class="radio-options">

            <label v-for="option in options" :key="option.value" class="radio-option">

                <input
                    type="radio"
                    :name="name"
                    :value="option.value"
                    :checked="option.value === value"
                    @change="handleChange" />

                <span class="radio-label">{{ option.label }}</span>

            </label>

        </div>

        <div class="error-wrp">

            <small v-for="(val, key) in errors" :key="key" v-show="val">{{ key }}</small>

        </div>

    </div>

</template>

<script>
export default {
    props: {
        name: String,
        value: String,
        options: Array, // [{ value: 'one', label: 'One' }, ...]
        label: String,
        errors: {
            type: Object,
            default: () => ({}),
        },
    },
    methods: {
        handleChange(event) {
            const selectedValue = event.target.value;
            this.$emit('input', selectedValue);
        },
    },
};
</script>

<style scoped>
.pure-material-label {
  display: block;
  margin-bottom: 16px;
  font-size: 85%;
  line-height: 18px;
  color: #707070;
  font-weight: 400;
  letter-spacing: 0.009375em;
}
.custom-radio-group {
  width: 100%;
  font-size: 17px;
  color: #707070;
  font-weight: 300;
  border: 1px solid var(--gold_outline, #e2b007);
  border-radius: 4px;
  padding: 18px 16px 12px 16px;
  background: #fff;
  position: relative;
}
.radio-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}
input[type="radio"] {
  appearance: none;
  -webkit-appearance: none;
  width: 22px;
  height: 22px;
  border: 2px solid var(--gold_outline, #e2b007);
  border-radius: 50%;
  background: #fff;
  outline: none;
  transition: border-color 0.2s;
  margin: 0;
  position: relative;
}
input[type="radio"]:checked {
  border-color: var(--darkgreen);
}
input[type="radio"]:checked::before {
  content: '';
  display: block;
  position: absolute;
  left: 4px;
  top: 4px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--darkgreen);
}
input[type="radio"]:focus {
  box-shadow: 0 0 0 2px rgba(226, 176, 7, 0.2);
}
.radio-label {
  margin-left: 12px;
  font-size: 16px;
}
.error-wrp {
  min-height: 16px;
  display: flex;
  flex-direction: column;
}
.error-wrp small {
  color: #f66;
}
</style>

