<template>

    <label class="custom-checkbox" :class="{ disabled: disabled }">

        <input
            type="checkbox"
            :id="id"
            :name="name"
            :value="value"
            :checked="checked"
            :disabled="disabled"
            @change="handleChange" />

        <span class="checkmark"></span>

        <span v-if="label" class="checkbox-label">{{ label }}</span>

        <slot></slot>

    </label>

</template>

<script>
export default {
    name: 'CustomCheckbox',
    props: {
        id: String,
        name: String,
        value: [String, Number, Boolean],
        checked: <PERSON><PERSON><PERSON>,
        disabled: {
            type: <PERSON><PERSON><PERSON>,
            default: false,
        },
        label: String,
    },
    methods: {
        handleChange(event) {
            this.$emit('change', event.target.checked);
            this.$emit('input', event.target.checked);
        },
    },
};
</script>

<style scoped>
.custom-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    position: relative;
    font-size: 14px;
    line-height: 1.5;
}

.custom-checkbox.disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

.custom-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    height: 18px;
    width: 18px;
    background-color: #fff;
    border: 2px solid #ddd;
    border-radius: 3px;
    position: relative;
    transition: all 0.2s ease;
    margin-right: 8px;
    flex-shrink: 0;
}

.custom-checkbox:hover input ~ .checkmark {
    border-color: var(--button_and_green_text);
}

.custom-checkbox input:checked ~ .checkmark {
    background-color: var(--button_and_green_text);
    border-color: var(--button_and_green_text);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 4px;
    top: 1px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.custom-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.custom-checkbox input:disabled ~ .checkmark {
    background-color: #f5f5f5;
    border-color: #ddd;
}

.checkbox-label {
    color: #333;
    font-weight: 500;
}

.custom-checkbox.disabled .checkbox-label {
    color: #999;
}
</style>

