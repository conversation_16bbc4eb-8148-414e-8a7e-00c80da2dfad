<template>

    <transition :enter-active-class="enterActiveClass" :leave-active-class="leaveActiveClass">

        <div v-show="show" class="account_popup">

            <div class="account_popup_ex">

                <img @click="$emit('close')" src="/img/ex_grey.svg" />

            </div>

            <div class="wrapper">

                <div class="account_popup_inner">

                    <slot />

                </div>

            </div>

        </div>

    </transition>

</template>

<script>
export default {
    name: 'CustomModal',
    props: {
        show: {
            type: Boolean,
            required: true,
        },
        enterActiveClass: {
            type: String,
            default: 'fadeIn faster animated',
        },
        leaveActiveClass: {
            type: String,
            default: 'fadeOut faster animated',
        },
    },
};
</script>

<style scoped>
.wrapper {
    overflow-y: auto;
    padding-top: 45px;
    padding-bottom: 30px;
    max-height: 100%;
    width: 100%;
}
.account_popup_inner{
    margin: 0 auto;
}
</style>

