<template>

    <custom-modal :show="show" @close="close">

        <div v-if="!isLoading">

            <div class="modal-header">

                <h2>{{ id ? 'Edit Wish List' : 'Add Wish List' }}</h2>

            </div>

            <div class="modal-body">

                <form @submit.prevent="save">

                    <custom-input
                        v-model="formData.name"
                        placeholder="Enter wish list name"
                        :value="formData.name"
                        :errors="errors.name"
                        :onError="!!errors.name" />

                    <custom-textarea
                        v-model="formData.description"
                        placeholder="Enter wish list description"
                        :value="formData.description"
                        :errors="errors.description"
                        :onError="!!errors.description" />

                    <custom-select
                        :options="addresses"
                        :value="String(formData.address_id)"
                        @change="(value) => (formData.address_id = Number(value))"
                        value-name="id"
                        key-name="full_address"
                        placeholder="Select an address"
                        :errors="errors.address_id"
                        :onError="!!errors.address_id" />

                    <custom-input
                        v-model="formData.desired_date"
                        placeholder="Enter desired date"
                        type="date"
                        :value="formData.desired_date"
                        :errors="errors.desired_date"
                        :onError="!!errors.desired_date" />

                    <custom-radio-group
                        :options="privacyLevels"
                        :value="formData.privacy_level"
                        name="privacy_level"
                        label="Privacy Settings"
                        @input="(value) => (formData.privacy_level = value)"
                        :errors="errors.privacy_level"
                        :onError="!!errors.privacy_level" />

                    <div class="form-actions">

                        <button type="submit" class="large_btn btn_style lg_bg" :disabled="!formData.name">
                             {{ id ? 'Update Wish List' : 'Add Wish List' }}
                        </button>

                        <button type="button" class="large_btn btn_style bordered_box" @click="close">Cancel</button>

                    </div>

                </form>

            </div>

        </div>

        <div v-else class="modal-loader">

            <inline-loader :show-force="true" />

        </div>

    </custom-modal>

</template>

<script>
import WishListService from '@/services/WishListService';
import CustomCheckbox from '../partials/CustomCheckbox.vue';
import CustomRadioGroup from '../partials/CustomRadioGroup.vue';
export default {
    name: 'ModalWishList',
    components: { CustomRadioGroup, CustomCheckbox },
    props: {
        show: {
            type: Boolean,
            required: true,
        },
        addresses: {
            type: Array,
            default: () => [],
        },
        id: {
            type: [Number, String],
            default: null,
        },
    },
    data() {
        return {
            isLoading: false,
            formData: {
                name: '',
                description: '',
                address_id: null,
                desired_date: null,
                privacy_level: 'private',
            },
            privacyLevels: [
                { value: 'private', label: 'Private' },
                { value: 'public', label: 'Public' },
                { value: 'shared', label: 'Shared' },
            ],
            errors: {},
        };
    },
    watch: {
        id: {
            immediate: true,
            async handler(newId) {
                this.isLoading = true;
                if (newId) {
                    const data = await WishListService.getWishListById(newId);
                    this.formData = {
                        name: data.name || '',
                        description: data.description || '',
                        address_id: data.address_id || null,
                        desired_date: data.desired_date || null,
                        privacy_level: data.privacy_level || 'private',
                    };
                } else {
                    this.resetForm();
                }
                this.isLoading = false;
            },
        },
    },
    methods: {
        close() {
            this.$emit('close');
        },
        resetForm() {
            this.formData = {
                name: '',
                description: '',
                address_id: null,
                desired_date: null,
                privacy_level: 'private',
            };
            this.errors = {};
        },
        async save() {
            this.errors = {};
            try {
                if (!this.formData.name) this.errors.name = 'Name is required';
                if (Object.keys(this.errors).length) return;
                if (this.id) {
                    await WishListService.updateWishList(this.id, this.formData);
                } else {
                    await WishListService.createWishList(this.formData);
                }
                this.resetForm();
                this.$emit('wishListUpdated');
                this.$emit('close');
            } catch (error) {
                if (error.response && error.response.data && error.response.data.errors) {
                    this.errors = error.response.data.errors;
                } else {
                    console.error('Error saving wish list:', error);
                }
            }
        },
    },
};
</script>

<style scoped>
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}
.form-actions{
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    margin-top: 20px;
}
.modal-loader{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
}
</style>

