const WishListService = {
    /**
     * Get all customer's wish lists
     */
    async getWishLists() {
        try {
            const response = await axios.get('/api/customer/wishlists');
            if (response.status === 200) {
                return response.data;
            } else {
                throw new Error('Failed to fetch wish lists');
            }
        } catch (error) {
            console.error('Error fetching wish lists:', error);
            throw error;
        }
    },

    /**
     * Get a specific wish list by ID
     */
    async getWishListById(id, token) {
        try {
            const response = await axios.get(`/api/wishlists/${id}?token=${token}`);
            if (response.status === 200) {
                return response.data;
            } else {
                throw new Error('Failed to fetch wish list');
            }
        } catch (error) {
            console.error('Error fetching wish list:', error);
            throw error;
        }
    },

    /**
     * Create a new wish list
     */
    async createWishList(data) {
        try {
            if (data.address_id) {
                data.address_id = parseInt(data.address_id, 10);
            }
            const response = await axios.post('/api/customer/wishlists', data);
            if (response.status === 201) {
                return response.data;
            } else {
                throw new Error('Failed to create wish list');
            }
        } catch (error) {
            console.error('Error creating wish list:', error);
            throw error;
        }
    },

    /**
     * Update an existing wish list
     */
    async updateWishList(id, data) {
        try {
            const response = await axios.put(`/api/customer/wishlists/${id}`, data);
            if (response.status === 200) {
                return response.data;
            } else {
                throw new Error('Failed to update wish list');
            }
        } catch (error) {
            console.error('Error updating wish list:', error);
            throw error;
        }
    },

    /**
     * Delete a wish list
     */
    async deleteWishList(id) {
        try {
            const response = await axios.delete(`/api/customer/wishlists/${id}`);
            if (response.status === 200 || response.status === 204) {
                return true;
            } else {
                throw new Error('Failed to delete wish list');
            }
        } catch (error) {
            console.error('Error deleting wish list:', error);
            throw error;
        }
    },

    async getPublicWishLists(filters = {}, page = 1, perPage = 10) {
        try {
            const params = new URLSearchParams();

            // Add filters to params
            if (filters.query) {
                params.append('query', filters.query);
            }
            if (filters.sort_by) {
                params.append('sort_by', filters.sort_by);
            }
            params.append('page', String(page));
            params.append('per_page', String(perPage));

            const response = await axios.get(`/api/wishlists/?${params.toString()}`);
            if (response.status === 200) {
                return response.data;
            } else {
                throw new Error('Failed to fetch public wish lists');
            }
        } catch (error) {
            console.error('Error fetching public wish lists:', error);
            throw error;
        }
    }
};

export default WishListService;
