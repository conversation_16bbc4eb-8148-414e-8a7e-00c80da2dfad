const CustomerService = {
    async getAddresses() {
        try {
            const response = await axios.get('/api/customer/addresses');
            if (response.status === 200) {
                return response.data;
            } else {
                throw new Error('Failed to fetch addresses');
            }
        } catch (error) {
            console.error('Error fetching addresses:', error);
            throw error;
        }
    },
};

export default CustomerService;
