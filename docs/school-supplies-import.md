# School Supplies CSV Import Script

## Overview
This Laravel console command imports product data from a CSV file into the products table, specifically designed for school supplies with automatic category assignment and media handling.

## Command Usage

```bash
# Basic import
php artisan import:school-supplies

# Specify custom file
php artisan import:school-supplies --file="my-school-supplies.csv"

# Process in smaller chunks (default is 50)
php artisan import:school-supplies --chunk=25

# Dry run to test without making changes
php artisan import:school-supplies --dry-run

# Combined options
php artisan import:school-supplies --file="supplies.csv" --chunk=100 --dry-run
```

## CSV File Format

### File Location
Place your CSV file in the `storage/` directory (e.g., `storage/school supplies.csv`)

### Column Structure (1-based indexing)
| Column | Field | Description | Example |
|--------|-------|-------------|---------|
| 1 | Title | Product name (required for new products) | "Pencil Set - 12 Pack" |
| 2 | Description | Product description | "High quality wooden pencils" |
| 3 | Skip | Ignored column | - |
| 4 | Skip | Ignored column | - |
| 5 | Skip | Ignored column | - |
| 6 | Published | Boolean (true/false, 1/0, yes/no) | true |
| 7 | SKU | Stock keeping unit | "PEN001" |
| 8 | Weight (grams) | Weight in grams (auto-converted to ounces) | 85 |
| 9 | Stock | Quantity available | 100 |
| 10 | Store Price | Selling price | 5.99 |
| 11 | List Price | MSRP/List price | 7.99 |
| 12 | Barcode | Product barcode | 123456789012 |
| 13 | Image URL | URL to product image | https://example.com/image.jpg |
| 14 | Cost Price | Cost/wholesale price | 3.50 |
| 15 | Skip | Ignored column | - |

### Special Handling

#### Additional Images
When Column 1 (Title) is empty but Column 13 (Image URL) has a value, the row is treated as an additional image for the most recently created product.

Example:
```csv
"Notebook - Spiral","College ruled notebook",,,,true,NB001,200,50,2.99,3.99,123456789013,https://example.com/notebook.jpg,1.75,
,"Additional back view",,,,,,,,,,https://example.com/notebook-back.jpg,,
```

#### Weight Conversion
Weights are automatically converted from grams to ounces using the formula:
```
ounces = grams ÷ 28.3495
```

#### Boolean Values
The Published field accepts various boolean formats:
- `true`, `false`
- `1`, `0`
- `yes`, `no`
- `on`, `off`

## Features

### Automatic Category Assignment
All imported products are automatically assigned to the "School Supplies" category (ID: 394).

### Media Handling
- Downloads images from URLs using Laravel Media Library's `addMediaFromUrl()` method
- Validates image URLs and file extensions (jpg, jpeg, png, gif, webp)
- Handles failed downloads gracefully with logging
- Supports multiple images per product

### Error Handling
- Validates CSV file existence
- Checks for required category
- Handles network errors for image downloads
- Logs all errors and warnings
- Continues processing even if individual rows fail

### Performance Features
- Processes data in configurable chunks (default: 50 rows)
- Uses database transactions for data integrity
- Progress reporting during import
- Memory-efficient CSV reading

### Logging
All operations are logged to Laravel's default log with context:
- Product creation success/failure
- Image download success/failure
- Row processing errors
- Import statistics

## Output Example

```
Starting import from: school supplies.csv
Category: School Supplies (ID: 394)
Header: Title | Description | Skip1 | Skip2 | Skip3 | Published | SKU | Weight(grams) | Stock | Store Price | List Price | Barcode | Image URL | Cost Price | Skip4
Processed 50 rows. Created: 45 products, Added: 52 images
Processed 100 rows. Created: 92 products, Added: 108 images

=== Import Summary ===
Total rows processed: 100
Products created: 92
Images added: 108
Rows skipped: 5
Rows with errors: 3
======================
```

## Database Fields Mapped

| CSV Column | Database Field | Notes |
|------------|----------------|-------|
| Title | title, slug | Slug auto-generated from title |
| Description | description | - |
| Published | visibility | Boolean conversion |
| SKU | sku | - |
| Weight (grams) | weight | Converted to ounces |
| Stock | website_quantity, store_quantity | Same value for both |
| Store Price | store_price | - |
| List Price | list_price | - |
| Barcode | barcode | - |
| Cost Price | cost_price | - |
| - | track_inventory | Set to true if quantity > 0 |
| - | item_type | Always set to 'physical' |

## Error Recovery

The script is designed to be resumable:
- Failed image downloads don't stop the import
- Individual row errors are logged but don't halt processing
- Database transactions ensure data integrity
- Dry run mode allows testing before actual import

## Requirements

- Laravel Media Library configured
- Category with ID 394 must exist
- Internet access for image downloads
- Sufficient storage space for downloaded images
- PHP extensions: curl, gd/imagick (for image processing)

## Troubleshooting

### Common Issues

1. **Category not found**: Ensure category ID 394 exists
2. **Image download failures**: Check URL validity and network connectivity
3. **Memory issues**: Reduce chunk size with `--chunk=25`
4. **Permission errors**: Ensure storage directory is writable

### Log Locations
- Laravel logs: `storage/logs/laravel.log`
- Import-specific entries tagged with context data

## Sample CSV
A sample CSV file is provided at `storage/school-supplies-sample.csv` for testing purposes.
