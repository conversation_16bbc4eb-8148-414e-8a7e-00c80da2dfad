<template>
    <PanelItem :index="index" :field="field">
        <template #value>
            <div class="or-products-wrapper">
                <div class="sing-prod-wrap" v-for="(prod,index) in products" :key="index">
                    <div class="or-single-prod">
                        <div class="or-img">
                            <img :src="prod.media" :alt="prod.title">
                        </div>
                        <div class="or-prod-info">
                            <router-link
                                class="or-prod-title"
                                :to="{
                                    name:'custom-edit',
                                    params:{
                                        component:'products-edit-view',
                                        resourceId:prod.product_id,
                                        resourceName:'products'
                                    }
                                }"
                            >{{prod.title}}</router-link>
                            <p v-if="prod.sku" class="or-prod-sku">SKU: {{prod.sku}}<span class="or-prod-stock"></span></p>
                            <p v-if="prod.meta" class="or-prod-sku">{{metaString(prod.meta)}}</p>
                            <p v-if="prod.returned && prod.returned.string"
                                 class="txt-returned"
                                 >{{returnedString(prod.returned)}}</p>
                            <p v-if="prod.status == 'cancelled'" class="txt-cancelled">Cancelled</p>
                        </div>
                        <p class="or-prod-price">{{prod.price | currency }}</p>
                        <!-- <p class="or-prod-price">{{prod.price * prod.quantity |currency}}</p> -->
                    </div>
                    <div>
                        <p class="gift-option"
                        v-if="prod.gift_options"
                        >{{`Gift Option: ${prod.gift_options.name} | Occasion: ${prod.gift_options.giftNote.name} `}}<span
                        v-if="prod.gift_options.occasionText" @click="printMsg(prod.gift_options.gift_note_id)">Gift Note</span></p>
                    </div>
                </div>
            </div>
        </template>
    </PanelItem>
</template>

<script>
import _ from 'lodash'
export default {
    props: ['resource', 'resourceName', 'resourceId', 'field'],

    data() {
        return {
            products : []
        }
    },

    mounted() {
        this.products = this.field.value
    },

    methods:{
        fill(formData) {
            formData.append(this.field.attribute, this.trueValue || '')
        },

        returnedString(returned){
            if(returned.status){
                return `${_.capitalize(returned.status)} return ${returned.string == '1' ? '': returned.string}`
            }
        },

        printMsg(id){
            this.$router.push({
                name:'detail',
                params:{
                    resourceId:id,
                    resourceName:'gift-notes'
                }
            })
        },

        metaString(meta) {
            if(typeof(meta) == "string"){
				meta = JSON.parse(meta)
			}
            return _.map(meta, (val, key, index) => {
                return `${key}: ${val}`
            }).join(' | ')
        },
    },


    filters:{
        currency(value){
            if(value){
                return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value)
            }
        }
    }
}
</script>
<style scoped>
.or-products-wrapper{
    border: #E3E7EB solid 1px;
    border-radius: 5px;
}
.sing-prod-wrap{
    padding: 20px;
    border-bottom: #EFF1F4 solid 1px;
}
.or-single-prod{
    display: flex;
    align-items: center;
}
.sing-prod-wrap:last-child{
    border-bottom: none;
}
.or-img{
    height: 72px;
    width: 72px;
    align-self: flex-start;
}
.or-img img{
    height: 100%;
    width: 100%;
    object-fit: contain;
}
.or-prod-info{
    margin-left: 15px;
    margin-right: 15px;
    display: flex;
    flex-direction: column;
    text-overflow: ellipsis;
    width: 50%;
}
.or-prod-title{
    color: #4099DE;
    font-size: 17px;
    line-height: 22px;
}
.or-prod-sku{
    margin-top: 6px;
    color: #A8ADB4;
    font-size: 14px;
    font-weight: 300;
}
.txt-returned,.txt-cancelled{
    margin-top: 6px;
    color: #F2C766;
    font-size: 14px;
    font-weight: 500;
}
.txt-cancelled{
    color: red;
}
.or-prod-price{
    margin-right: 40px;
    color: #525860;
    font-size: 18px;
    font-weight: 500;
    width: 25%;
}
.gift-option{
    margin-top: 15px;
    margin-left: 85px;
    font-size: 14px;
    color: black;
    font-weight: 500;
}
.gift-option span{
    color: #4099DE;
    cursor: pointer;
    margin-left: 10px;
}
.or-prod-price:last-child{
    margin-right: 10px;
    width: initial;
}
</style>
