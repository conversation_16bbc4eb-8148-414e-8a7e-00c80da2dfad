<?php

namespace Capitalc\CustomCsvImport\Http\Controllers;

use SimonHamp\LaravelNovaCsvImport\Http\Controllers\ImportController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Jobs\ProcessCsvImportJob;
use Laravel\Nova\Http\Requests\NovaRequest;

class QueuedImportController extends ImportController
{
    /**
     * Override the import method to queue the job instead of processing synchronously
     */
    public function import(NovaRequest $request)
    {
        // Get the file parameter from the request (this is the full filename with extension)
        $file = $request->get('file');
        $filePath = "csv-import/{$file}";

        // Verify the file exists
        if (!Storage::exists($filePath)) {
            \Log::error('CSV file not found during import', [
                'requested_file' => $file,
                'constructed_path' => $filePath,
                'storage_disk' => config('filesystems.default'),
                'files_in_directory' => Storage::files('csv-import')
            ]);

            return response()->json([
                'error' => 'CSV file not found',
                'file' => $file,
                'path' => $filePath
            ], 404);
        }

        // Get the configuration for this file (matches base ImportController logic)
        if (!$config = $this->getConfigForFile($file)) {
            return response()->json([
                'error' => 'Configuration file not found. Please reconfigure the import.',
                'redirect' => "/csv-import/configure/{$file}"
            ], 400);
        }

        // Get the resource from the config file (not from request parameters)
        $resource = $config['resource'];

        if (!$resource) {
            return response()->json([
                'error' => 'Resource not configured. Please reconfigure the import.',
                'redirect' => "/csv-import/configure/{$file}"
            ], 400);
        }

        try {
            $resourceClass = $this->getResourceClass($resource);
            $modelClass = $resourceClass::$model;
        } catch (\Exception $e) {
            \Log::error('Invalid resource class during import', [
                'resource' => $resource,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Invalid resource class: ' . $resource
            ], 400);
        }

        // Get all the configuration from the config file (matches base ImportController)
        $attributeMap = $config['mappings'] ?? [];
        $rules = $this->getRulesForResource($resourceClass);
        $meta = $config['meta'] ?? [];
        $randomStringSettings = $config['random'] ?? [];
        $customValues = $config['values'] ?? [];
        $combinedValues = $config['combined'] ?? [];

        // Initialize the results file with processing status
        // Extract hash from filename for results file (remove extension)
        $hash = pathinfo($file, PATHINFO_FILENAME);
        $resultsPath = "csv-import/{$hash}.csv.results.json";
        $initialResults = [
            'status' => 'queued',
            'started_at' => now()->toISOString(),
            'completed_at' => null,
            'total_rows' => 0,
            'processed_rows' => 0,
            'successful_rows' => 0,
            'failed_rows' => 0,
            'imported' => 0, // ✅ Add imported key for base controller compatibility
            'failures' => [],
            'errors' => []
        ];
        
        Storage::put($resultsPath, json_encode($initialResults, JSON_PRETTY_PRINT));

        // Dispatch the job to the default queue for background processing
        try {
            ProcessCsvImportJob::dispatch(
                $filePath,
                $resourceClass,
                $modelClass,
                $attributeMap,
                $rules,
                $meta,
                $randomStringSettings,
                $customValues,
                $combinedValues
            );

            \Log::info('CSV import job dispatched successfully', [
                'file' => $file,
                'resource' => $resourceClass,
                'model' => $modelClass
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to dispatch CSV import job', [
                'file' => $file,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to queue import job: ' . $e->getMessage()
            ], 500);
        }

        // Return success response immediately
        return response()->json([
            'message' => 'Import job queued successfully',
            'status' => 'queued',
            'results_url' => route('nova.vendor.csv-import.results', ['file' => $hash])
        ]);
    }

    /**
     * Get the resource class from the resource name/key
     */
    private function getResourceClass(string $resource): string
    {
        // Handle common Nova resource keys directly (more reliable than Nova::resourceInstanceForKey)
        $resourceMap = [
            'products' => 'App\\Nova\\Product',
            'orders' => 'App\\Nova\\Order',
            'customers' => 'App\\Nova\\Customer',
            'categories' => 'App\\Nova\\Category',
            'vendors' => 'App\\Nova\\Vendor',
            // Add more mappings as needed
        ];

        // Check if it's a known resource key
        if (isset($resourceMap[$resource])) {
            $resourceClass = $resourceMap[$resource];
            if (class_exists($resourceClass)) {
                return $resourceClass;
            }
        }

        // Try to find the resource by Nova key (fallback)
        try {
            $novaResource = \Laravel\Nova\Nova::resourceInstanceForKey($resource);
            if ($novaResource) {
                return get_class($novaResource);
            }
        } catch (\Exception $e) {
            // Continue to next fallback
        }

        // Final fallback: treat as class name with namespace conversion
        $resourceClass = str_replace('/', '\\', $resource);

        if (!class_exists($resourceClass)) {
            throw new \Exception("Resource not found: {$resource}");
        }

        return $resourceClass;
    }

    /**
     * Get validation rules for the resource
     */
    private function getRulesForResource(string $resourceClass): array
    {
        try {
            // Create a temporary instance to get the rules
            $modelClass = $resourceClass::$model;
            $resource = new $resourceClass(new $modelClass());

            // Extract validation rules from the Nova resource (matches base ImportController)
            $request = app(\Laravel\Nova\Http\Requests\NovaRequest::class);
            $rules = $this->extractValidationRules($resource, $request)->toArray();

            // Use our CustomImporter to apply custom logic (like making Title optional)
            $importer = new \Capitalc\CustomCsvImport\CustomImporter();
            $importer->setResource($resource);
            $importer->setModelClass($modelClass);
            $importer->setRules($rules); // ✅ Set the rules before calling rules()

            return $importer->rules();
        } catch (\Exception $e) {
            \Log::warning('Could not get rules for resource', [
                'resource' => $resourceClass,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Extract validation rules from Nova resource (matches base ImportController)
     */
    protected function extractValidationRules(\Laravel\Nova\Resource $resource, \Laravel\Nova\Http\Requests\NovaRequest $request): \Illuminate\Support\Collection
    {
        return collect($resource::rulesForCreation($request))->mapWithKeys(function ($rule, $key) {
            foreach ($rule as $i => $r) {
                if (! is_object($r)) {
                    continue;
                }

                if ($r instanceof \Laravel\Nova\Rules\Relatable) {
                    $rule[$i] = function () {
                        return true;
                    };
                }
            }

            return [$key => $rule];
        });
    }

    /**
     * Override the results method to handle real-time updates
     */
    public function results(Request $request)
    {
        $file = $request->get('file');
        // Extract hash from filename for results file (remove extension)
        $hash = pathinfo($file, PATHINFO_FILENAME);
        $resultsPath = "csv-import/{$hash}.csv.results.json";
        
        if (!Storage::exists($resultsPath)) {
            return response()->json([
                'error' => 'Results file not found'
            ], 404);
        }
        
        $results = json_decode(Storage::get($resultsPath), true);
        
        // Add additional metadata for the frontend
        $results['is_processing'] = in_array($results['status'], ['queued', 'processing']);
        $results['progress_percentage'] = $results['total_rows'] > 0 
            ? round(($results['processed_rows'] / $results['total_rows']) * 100, 2)
            : 0;
        
        return response()->json($results);
    }

    /**
     * Get configuration for a file (inherited from base ImportController logic)
     */
    protected function getConfigForFile(string $file): array
    {
        $configPath = $this->getConfigFilePath($file);

        if (!Storage::exists($configPath)) {
            return [];
        }

        $config = json_decode(Storage::get($configPath), true) ?? [];

        // Ensure required keys exist with defaults (matches base controller)
        $config['values'] = $config['values'] ?? [];
        $config['modifiers'] = $config['modifiers'] ?? [];
        $config['combined'] = $config['combined'] ?? [];
        $config['mappings'] = $config['mappings'] ?? [];
        $config['random'] = $config['random'] ?? [];
        $config['meta'] = $config['meta'] ?? [];

        return $config;
    }

    /**
     * Get config file path (matches base ImportController)
     */
    protected function getConfigFilePath(string $file): string
    {
        $hash = pathinfo($file, PATHINFO_FILENAME);
        return "csv-import/{$hash}.csv.config.json";
    }
}
