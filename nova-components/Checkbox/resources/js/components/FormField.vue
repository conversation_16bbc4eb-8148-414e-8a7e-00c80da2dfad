<template>
    <DefaultField
        :field="field"
        :errors="errors"
        :show-help-text="showHelpText"
        :full-width-content="fullWidthContent"
    >
        <template #field>
            <label class="switch">
                <input
                    :id="field.attribute"
                    type="checkbox"
                    v-model="value"
                />
                <span class="slider"></span>
            </label>
        </template>
    </DefaultField>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova'

export default {
    mixins: [FormField, HandlesValidationErrors],

    props: ['resourceName', 'resourceId', 'field'],

    data() {
        return {
            hidden: false,
        }
    },
    mounted() {
        if (this.field.hidden) {
            this.hidden = true;
        }
    },
    methods: {
        setInitialValue() {
            this.value = this.field.value ? Boolean(this.field.value) : false;
        },

        fill(formData) {
            formData.append(this.field.attribute, Boolean(this.trueValue) || false)
        },

        handleChange(value) {
            this.value = value
        },
    },
    computed: {
        checked() {
            return Boolean(this.value)
        },

        trueValue() {
            return +this.checked
        },
    },

  watch: {
    value: {
      deep: true,
      handler(value) {
        this.$emit('changed', value)
      }
    }
  },
}
</script>


<style scoped>
.switch {
    position: relative;
    display: inline-block;
    width: 46px;
    height: 26px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(var(--colors-gray-200));
    transition: 0.4s;
    border-radius: 20px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: rgba(var(--colors-primary-500));
}

input:checked + .slider:before {
    transform: translateX(20px);
}
</style>

