<template>
    <PanelItem :index="index" :field="field">
        <template #value>
            <span
                class="inline-block rounded-full w-2 h-2"
                :class="{ 'bg-success': field.value, 'bg-danger': !field.value }"
            />
        </template>
    </PanelItem>
</template>

<script>
export default {
    props: ['index', 'resource', 'resourceName', 'resourceId', 'field'],
}
</script>
