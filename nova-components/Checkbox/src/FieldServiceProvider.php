<?php

namespace Capitalc\Checkbox;

use Laravel\Nova\Nova;
use Laravel\Nova\Events\ServingNova;
use Illuminate\Support\ServiceProvider;

class FieldServiceProvider extends ServiceProvider
{
    public function boot()
    {
        Nova::serving(function (ServingNova $event) {
            Nova::script('checkbox', __DIR__.'/../dist/js/field.js');
            Nova::style('checkbox', __DIR__.'/../dist/css/field.css');
        });
    }

    public function register()
    {
        //
    }
}
