<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWishListItemsTable extends Migration
{
    public function up(): void
    {
        Schema::create('wish_list_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('wish_list_id')->constrained()->onDelete('cascade');
            $table->unsignedInteger('product_id');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->unsignedInteger('variation_info_id')->nullable();
            $table->foreign('variation_info_id')->references('id')->on('variation_infos')->onDelete('cascade');
            $table->integer('quantity')->default(1);
            $table->integer('order')->default(0);
            $table->text('notes')->nullable();
            $table->integer('purchase_quantity')->default(0);
            $table->timestamps();

            $table->index(['wish_list_id', 'product_id']);
            $table->index('order');
            $table->unique(['wish_list_id', 'product_id', 'variation_info_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('wish_list_items');
    }
}
