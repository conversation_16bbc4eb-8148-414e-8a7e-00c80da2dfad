<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRecipientIdAndWishlistIdToBagTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('bag', function (Blueprint $table) {
            $table->unsignedBigInteger('recipient_id')->nullable()->after('customer_id');
            $table->unsignedBigInteger('wishlist_id')->nullable()->after('recipient_id');
            $table->enum('type', ['personal', 'cart', 'wishlist', 'gift'])->default('personal')->after('wishlist_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('bag', function (Blueprint $table) {
            $table->dropColumn(['recipient_id', 'wishlist_id', 'type']);
        });
    }
}
