<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWishListsTable extends Migration
{
    public function up(): void
    {
        Schema::create('wish_lists', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('address_id')->nullable()->constrained()->onDelete('set null');
            $table->date('desired_date')->nullable();
            $table->enum('privacy_level', \App\Enums\WishListPrivacyLevel::values())
                ->default(\App\Enums\WishListPrivacyLevel::PRIVATE);
            $table->string('share_token')->nullable()->unique();
            $table->timestamps();
            $table->index('desired_date');
            $table->index('share_token');
            $table->boolean('allow_purchase_from_other_products')
                ->default(false)
                ->comment('Allows purchasing from products not in the wishlist');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('wish_lists');
    }
}
